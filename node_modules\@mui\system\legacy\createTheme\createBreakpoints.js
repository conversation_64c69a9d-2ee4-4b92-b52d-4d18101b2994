import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import _extends from "@babel/runtime/helpers/esm/extends";
// Sorted ASC by size. That's important.
// It can't be configured as it's used statically for propTypes.
export var breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];
var sortBreakpointsValues = function sortBreakpointsValues(values) {
  var breakpointsAsArray = Object.keys(values).map(function (key) {
    return {
      key: key,
      val: values[key]
    };
  }) || [];
  // Sort in ascending order
  breakpointsAsArray.sort(function (breakpoint1, breakpoint2) {
    return breakpoint1.val - breakpoint2.val;
  });
  return breakpointsAsArray.reduce(function (acc, obj) {
    return _extends({}, acc, _defineProperty({}, obj.key, obj.val));
  }, {});
};

// Keep in mind that @media is inclusive by the CSS specification.
export default function createBreakpoints(breakpoints) {
  var _breakpoints$values = breakpoints.values,
    values = _breakpoints$values === void 0 ? {
      xs: 0,
      // phone
      sm: 600,
      // tablet
      md: 900,
      // small laptop
      lg: 1200,
      // desktop
      xl: 1536 // large screen
    } : _breakpoints$values,
    _breakpoints$unit = breakpoints.unit,
    unit = _breakpoints$unit === void 0 ? 'px' : _breakpoints$unit,
    _breakpoints$step = breakpoints.step,
    step = _breakpoints$step === void 0 ? 5 : _breakpoints$step,
    other = _objectWithoutProperties(breakpoints, ["values", "unit", "step"]);
  var sortedValues = sortBreakpointsValues(values);
  var keys = Object.keys(sortedValues);
  function up(key) {
    var value = typeof values[key] === 'number' ? values[key] : key;
    return "@media (min-width:".concat(value).concat(unit, ")");
  }
  function down(key) {
    var value = typeof values[key] === 'number' ? values[key] : key;
    return "@media (max-width:".concat(value - step / 100).concat(unit, ")");
  }
  function between(start, end) {
    var endIndex = keys.indexOf(end);
    return "@media (min-width:".concat(typeof values[start] === 'number' ? values[start] : start).concat(unit, ") and ") + "(max-width:".concat((endIndex !== -1 && typeof values[keys[endIndex]] === 'number' ? values[keys[endIndex]] : end) - step / 100).concat(unit, ")");
  }
  function only(key) {
    if (keys.indexOf(key) + 1 < keys.length) {
      return between(key, keys[keys.indexOf(key) + 1]);
    }
    return up(key);
  }
  function not(key) {
    // handle first and last key separately, for better readability
    var keyIndex = keys.indexOf(key);
    if (keyIndex === 0) {
      return up(keys[1]);
    }
    if (keyIndex === keys.length - 1) {
      return down(keys[keyIndex]);
    }
    return between(key, keys[keys.indexOf(key) + 1]).replace('@media', '@media not all and');
  }
  return _extends({
    keys: keys,
    values: sortedValues,
    up: up,
    down: down,
    between: between,
    only: only,
    not: not,
    unit: unit
  }, other);
}