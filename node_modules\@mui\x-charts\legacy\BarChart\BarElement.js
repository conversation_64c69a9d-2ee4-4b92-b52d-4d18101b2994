import _extends from "@babel/runtime/helpers/esm/extends";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["id", "dataIndex", "classes", "color", "highlightScope", "slots", "slotProps", "style"];
import * as React from 'react';
import PropTypes from 'prop-types';
import composeClasses from '@mui/utils/composeClasses';
import { useSlotProps } from '@mui/base/utils';
import generateUtilityClass from '@mui/utils/generateUtilityClass';
import { styled } from '@mui/material/styles';
import { color as d3Color } from 'd3-color';
import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import { animated } from '@react-spring/web';
import { getIsFaded, getIsHighlighted, useInteractionItemProps } from '../hooks/useInteractionItemProps';
import { InteractionContext } from '../context/InteractionProvider';
import { jsx as _jsx } from "react/jsx-runtime";
export function getBarElementUtilityClass(slot) {
  return generateUtilityClass('MuiBarElement', slot);
}
export var barElementClasses = generateUtilityClasses('MuiBarElement', ['root']);
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes,
    id = ownerState.id;
  var slots = {
    root: ['root', "series-".concat(id)]
  };
  return composeClasses(slots, getBarElementUtilityClass, classes);
};
export var BarElementPath = styled(animated.rect, {
  name: 'MuiBarElement',
  slot: 'Root',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.root;
  }
})(function (_ref) {
  var ownerState = _ref.ownerState;
  return {
    stroke: 'none',
    shapeRendering: 'crispEdges',
    fill: ownerState.isHighlighted ? d3Color(ownerState.color).brighter(0.5).formatHex() : ownerState.color,
    transition: 'opacity 0.2s ease-in, fill 0.2s ease-in',
    opacity: ownerState.isFaded && 0.3 || 1
  };
});
function BarElement(props) {
  var _slots$bar;
  var id = props.id,
    dataIndex = props.dataIndex,
    innerClasses = props.classes,
    color = props.color,
    highlightScope = props.highlightScope,
    slots = props.slots,
    slotProps = props.slotProps,
    style = props.style,
    other = _objectWithoutProperties(props, _excluded);
  var getInteractionItemProps = useInteractionItemProps(highlightScope);
  var _React$useContext = React.useContext(InteractionContext),
    item = _React$useContext.item;
  var isHighlighted = getIsHighlighted(item, {
    type: 'bar',
    seriesId: id,
    dataIndex: dataIndex
  }, highlightScope);
  var isFaded = !isHighlighted && getIsFaded(item, {
    type: 'bar',
    seriesId: id,
    dataIndex: dataIndex
  }, highlightScope);
  var ownerState = {
    id: id,
    dataIndex: dataIndex,
    classes: innerClasses,
    color: color,
    isFaded: isFaded,
    isHighlighted: isHighlighted
  };
  var classes = useUtilityClasses(ownerState);
  var Bar = (_slots$bar = slots == null ? void 0 : slots.bar) != null ? _slots$bar : BarElementPath;
  var barProps = useSlotProps({
    elementType: Bar,
    externalSlotProps: slotProps == null ? void 0 : slotProps.bar,
    additionalProps: _extends({}, other, getInteractionItemProps({
      type: 'bar',
      seriesId: id,
      dataIndex: dataIndex
    }), {
      style: style,
      className: classes.root
    }),
    ownerState: ownerState
  });
  return /*#__PURE__*/_jsx(Bar, _extends({}, barProps));
}
process.env.NODE_ENV !== "production" ? BarElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  classes: PropTypes.object,
  dataIndex: PropTypes.number.isRequired,
  highlightScope: PropTypes.shape({
    faded: PropTypes.oneOf(['global', 'none', 'series']),
    highlighted: PropTypes.oneOf(['item', 'none', 'series'])
  }),
  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: PropTypes.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: PropTypes.object
} : void 0;
export { BarElement };