import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _extends from "@babel/runtime/helpers/esm/extends";
import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
var _excluded = ["skipAnimation"];
import * as React from 'react';
import PropTypes from 'prop-types';
import { useTransition } from '@react-spring/web';
import { SeriesContext } from '../context/SeriesContextProvider';
import { CartesianContext } from '../context/CartesianContextProvider';
import { BarElement } from './BarElement';
import { isBandScaleConfig } from '../models/axis';
import { DEFAULT_X_AXIS_KEY, DEFAULT_Y_AXIS_KEY } from '../constants';
import { jsx as _jsx } from "react/jsx-runtime";
/**
 * Solution of the equations
 * W = barWidth * N + offset * (N-1)
 * offset / (offset + barWidth) = r
 * @param bandWidth The width available to place bars.
 * @param numberOfGroups The number of bars to place in that space.
 * @param gapRatio The ratio of the gap between bars over the bar width.
 * @returns The bar width and the offset between bars.
 */
function getBandSize(_ref) {
  var W = _ref.bandWidth,
    N = _ref.numberOfGroups,
    r = _ref.gapRatio;
  if (r === 0) {
    return {
      barWidth: W / N,
      offset: 0
    };
  }
  var barWidth = W / (N + (N - 1) * r);
  var offset = r * barWidth;
  return {
    barWidth: barWidth,
    offset: offset
  };
}
var useCompletedData = function useCompletedData() {
  var _React$useContext$bar;
  var seriesData = (_React$useContext$bar = React.useContext(SeriesContext).bar) != null ? _React$useContext$bar : {
    series: {},
    stackingGroups: [],
    seriesOrder: []
  };
  var axisData = React.useContext(CartesianContext);
  var series = seriesData.series,
    stackingGroups = seriesData.stackingGroups;
  var xAxis = axisData.xAxis,
    yAxis = axisData.yAxis,
    xAxisIds = axisData.xAxisIds,
    yAxisIds = axisData.yAxisIds;
  var defaultXAxisId = xAxisIds[0];
  var defaultYAxisId = yAxisIds[0];
  var data = stackingGroups.flatMap(function (_ref2, groupIndex) {
    var groupIds = _ref2.ids;
    return groupIds.flatMap(function (seriesId) {
      var _series$seriesId$xAxi, _series$seriesId$yAxi;
      var xAxisKey = (_series$seriesId$xAxi = series[seriesId].xAxisKey) != null ? _series$seriesId$xAxi : defaultXAxisId;
      var yAxisKey = (_series$seriesId$yAxi = series[seriesId].yAxisKey) != null ? _series$seriesId$yAxi : defaultYAxisId;
      var xAxisConfig = xAxis[xAxisKey];
      var yAxisConfig = yAxis[yAxisKey];
      var verticalLayout = series[seriesId].layout === 'vertical';
      var baseScaleConfig;
      if (verticalLayout) {
        if (!isBandScaleConfig(xAxisConfig)) {
          throw new Error("MUI-X-Charts: ".concat(xAxisKey === DEFAULT_X_AXIS_KEY ? 'The first `xAxis`' : "The x-axis with id \"".concat(xAxisKey, "\""), " shoud be of type \"band\" to display the bar series of id \"").concat(seriesId, "\""));
        }
        if (xAxis[xAxisKey].data === undefined) {
          throw new Error("MUI-X-Charts: ".concat(xAxisKey === DEFAULT_X_AXIS_KEY ? 'The first `xAxis`' : "The x-axis with id \"".concat(xAxisKey, "\""), " shoud have data property"));
        }
        baseScaleConfig = xAxisConfig;
      } else {
        if (!isBandScaleConfig(yAxisConfig)) {
          throw new Error("MUI-X-Charts: ".concat(yAxisKey === DEFAULT_Y_AXIS_KEY ? 'The first `yAxis`' : "The y-axis with id \"".concat(yAxisKey, "\""), " shoud be of type \"band\" to display the bar series of id \"").concat(seriesId, "\""));
        }
        if (yAxis[yAxisKey].data === undefined) {
          throw new Error("MUI-X-Charts: ".concat(yAxisKey === DEFAULT_Y_AXIS_KEY ? 'The first `yAxis`' : "The y-axis with id \"".concat(yAxisKey, "\""), " shoud have data property"));
        }
        baseScaleConfig = yAxisConfig;
      }
      var xScale = xAxisConfig.scale;
      var yScale = yAxisConfig.scale;
      var bandWidth = baseScaleConfig.scale.bandwidth();
      var _getBandSize = getBandSize({
          bandWidth: bandWidth,
          numberOfGroups: stackingGroups.length,
          gapRatio: baseScaleConfig.barGapRatio
        }),
        barWidth = _getBandSize.barWidth,
        offset = _getBandSize.offset;
      var barOffset = groupIndex * (barWidth + offset);
      var _series$seriesId = series[seriesId],
        stackedData = _series$seriesId.stackedData,
        color = _series$seriesId.color;
      return stackedData.map(function (values, dataIndex) {
        var _xAxis$xAxisKey$data, _yAxis$yAxisKey$data;
        var valueCoordinates = values.map(function (v) {
          return verticalLayout ? yScale(v) : xScale(v);
        });
        var minValueCoord = Math.min.apply(Math, _toConsumableArray(valueCoordinates));
        var maxValueCoord = Math.max.apply(Math, _toConsumableArray(valueCoordinates));
        return {
          seriesId: seriesId,
          dataIndex: dataIndex,
          layout: series[seriesId].layout,
          x: verticalLayout ? xScale((_xAxis$xAxisKey$data = xAxis[xAxisKey].data) == null ? void 0 : _xAxis$xAxisKey$data[dataIndex]) + barOffset : minValueCoord,
          y: verticalLayout ? minValueCoord : yScale((_yAxis$yAxisKey$data = yAxis[yAxisKey].data) == null ? void 0 : _yAxis$yAxisKey$data[dataIndex]) + barOffset,
          xOrigin: xScale(0),
          yOrigin: yScale(0),
          height: verticalLayout ? maxValueCoord - minValueCoord : barWidth,
          width: verticalLayout ? barWidth : maxValueCoord - minValueCoord,
          color: color,
          highlightScope: series[seriesId].highlightScope
        };
      });
    });
  });
  return data;
};
var getOutStyle = function getOutStyle(_ref3) {
  var layout = _ref3.layout,
    yOrigin = _ref3.yOrigin,
    x = _ref3.x,
    width = _ref3.width,
    y = _ref3.y,
    xOrigin = _ref3.xOrigin,
    height = _ref3.height;
  return _extends({}, layout === 'vertical' ? {
    y: yOrigin,
    x: x,
    height: 0,
    width: width
  } : {
    y: y,
    x: xOrigin,
    height: height,
    width: 0
  });
};
var getInStyle = function getInStyle(_ref4) {
  var x = _ref4.x,
    width = _ref4.width,
    y = _ref4.y,
    height = _ref4.height;
  return {
    y: y,
    x: x,
    height: height,
    width: width
  };
};

/**
 * Demos:
 *
 * - [Bars](https://mui.com/x/react-charts/bars/)
 * - [Bar demonstration](https://mui.com/x/react-charts/bar-demo/)
 * - [Stacking](https://mui.com/x/react-charts/stacking/)
 *
 * API:
 *
 * - [BarPlot API](https://mui.com/x/api/charts/bar-plot/)
 */
function BarPlot(props) {
  var completedData = useCompletedData();
  var skipAnimation = props.skipAnimation,
    other = _objectWithoutProperties(props, _excluded);
  var transition = useTransition(completedData, {
    keys: function keys(bar) {
      return "".concat(bar.seriesId, "-").concat(bar.dataIndex);
    },
    from: getOutStyle,
    leave: getOutStyle,
    enter: getInStyle,
    update: getInStyle,
    immediate: skipAnimation
  });
  return /*#__PURE__*/_jsx(React.Fragment, {
    children: transition(function (style, _ref5) {
      var seriesId = _ref5.seriesId,
        dataIndex = _ref5.dataIndex,
        color = _ref5.color,
        highlightScope = _ref5.highlightScope;
      return /*#__PURE__*/_jsx(BarElement, _extends({
        id: seriesId,
        dataIndex: dataIndex,
        highlightScope: highlightScope,
        color: color
      }, other, {
        style: style
      }));
    })
  });
}
process.env.NODE_ENV !== "production" ? BarPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * If `true`, animations are skiped.
   * @default false
   */
  skipAnimation: PropTypes.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: PropTypes.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: PropTypes.object
} : void 0;
export { BarPlot };