import _extends from "@babel/runtime/helpers/esm/extends";
import * as React from 'react';
import PropTypes from 'prop-types';
import { useSlotProps } from '@mui/base/utils';
import { SeriesContext } from '../context/SeriesContextProvider';
import { CartesianContext } from '../context/CartesianContextProvider';
import { DefaultChartsAxisTooltipContent } from './DefaultChartsAxisTooltipContent';
import { jsx as _jsx } from "react/jsx-runtime";
function ChartsAxisTooltipContent(props) {
  var content = props.content,
    contentProps = props.contentProps,
    axisData = props.axisData,
    sx = props.sx,
    classes = props.classes;
  var isXaxis = (axisData.x && axisData.x.index) !== undefined;
  var dataIndex = isXaxis ? axisData.x && axisData.x.index : axisData.y && axisData.y.index;
  var axisValue = isXaxis ? axisData.x && axisData.x.value : axisData.y && axisData.y.value;
  var _React$useContext = React.useContext(CartesianContext),
    xAxisIds = _React$useContext.xAxisIds,
    xAxis = _React$useContext.xAxis,
    yAxisIds = _React$useContext.yAxisIds,
    yAxis = _React$useContext.yAxis;
  var series = React.useContext(SeriesContext);
  var USED_AXIS_ID = isXaxis ? xAxisIds[0] : yAxisIds[0];
  var relevantSeries = React.useMemo(function () {
    var rep = [];
    Object.keys(series).filter(function (seriesType) {
      return ['bar', 'line', 'scatter'].includes(seriesType);
    }).forEach(function (seriesType) {
      series[seriesType].seriesOrder.forEach(function (seriesId) {
        var item = series[seriesType].series[seriesId];
        var axisKey = isXaxis ? item.xAxisKey : item.yAxisKey;
        if (axisKey === undefined || axisKey === USED_AXIS_ID) {
          rep.push(series[seriesType].series[seriesId]);
        }
      });
    });
    return rep;
  }, [USED_AXIS_ID, isXaxis, series]);
  var relevantAxis = React.useMemo(function () {
    return isXaxis ? xAxis[USED_AXIS_ID] : yAxis[USED_AXIS_ID];
  }, [USED_AXIS_ID, isXaxis, xAxis, yAxis]);
  var Content = content != null ? content : DefaultChartsAxisTooltipContent;
  var chartTooltipContentProps = useSlotProps({
    elementType: Content,
    externalSlotProps: contentProps,
    additionalProps: {
      axisData: axisData,
      series: relevantSeries,
      axis: relevantAxis,
      dataIndex: dataIndex,
      axisValue: axisValue,
      sx: sx,
      classes: classes
    },
    ownerState: {}
  });
  return /*#__PURE__*/_jsx(Content, _extends({}, chartTooltipContentProps));
}
process.env.NODE_ENV !== "production" ? ChartsAxisTooltipContent.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  axisData: PropTypes.shape({
    x: PropTypes.shape({
      index: PropTypes.number,
      value: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]).isRequired
    }),
    y: PropTypes.shape({
      index: PropTypes.number,
      value: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]).isRequired
    })
  }).isRequired,
  classes: PropTypes.object.isRequired,
  content: PropTypes.elementType,
  contentProps: PropTypes.shape({
    axis: PropTypes.object,
    axisData: PropTypes.shape({
      x: PropTypes.shape({
        index: PropTypes.number,
        value: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]).isRequired
      }),
      y: PropTypes.shape({
        index: PropTypes.number,
        value: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]).isRequired
      })
    }),
    axisValue: PropTypes.any,
    classes: PropTypes.object,
    dataIndex: PropTypes.number,
    series: PropTypes.arrayOf(PropTypes.object),
    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])
  }),
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])
} : void 0;
export { ChartsAxisTooltipContent };