import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import _extends from "@babel/runtime/helpers/esm/extends";
import * as React from 'react';
import { scaleBand, scalePoint } from 'd3-scale';
import PropTypes from 'prop-types';
import { getExtremumX as getBarExtremumX, getExtremumY as getBarExtremumY } from '../BarChart/extremums';
import { getExtremumX as getScatterExtremumX, getExtremumY as getScatterExtremumY } from '../ScatterChart/extremums';
import { getExtremumX as getLineExtremumX, getExtremumY as getLineExtremumY } from '../LineChart/extremums';
import { isBandScaleConfig, isPointScaleConfig } from '../models/axis';
import { getScale } from '../internals/getScale';
import { DrawingContext } from './DrawingProvider';
import { SeriesContext } from './SeriesContextProvider';
import { DEFAULT_X_AXIS_KEY, DEFAULT_Y_AXIS_KEY } from '../constants';
import { getTickNumber } from '../hooks/useTicks';
import { jsx as _jsx } from "react/jsx-runtime";
var DEFAULT_CATEGORY_GAP_RATIO = 0.2;
var DEFAULT_BAR_GAP_RATIO = 0.1;

// TODO: those might be better placed in a distinct file
var xExtremumGetters = {
  bar: getBarExtremumX,
  scatter: getScatterExtremumX,
  line: getLineExtremumX
};
var yExtremumGetters = {
  bar: getBarExtremumY,
  scatter: getScatterExtremumY,
  line: getLineExtremumY
};
export var CartesianContext = /*#__PURE__*/React.createContext({
  xAxis: {},
  yAxis: {},
  xAxisIds: [],
  yAxisIds: []
});

/**
 * API:
 *
 * - [CartesianContextProvider API](https://mui.com/x/api/charts/cartesian-context-provider/)
 */
function CartesianContextProvider(props) {
  var inXAxis = props.xAxis,
    inYAxis = props.yAxis,
    dataset = props.dataset,
    children = props.children;
  var formattedSeries = React.useContext(SeriesContext);
  var drawingArea = React.useContext(DrawingContext);
  var xAxis = React.useMemo(function () {
    return inXAxis == null ? void 0 : inXAxis.map(function (axisConfig) {
      var dataKey = axisConfig.dataKey;
      if (dataKey === undefined || axisConfig.data !== undefined) {
        return axisConfig;
      }
      if (dataset === undefined) {
        throw Error('MUI-X-Charts: x-axis uses `dataKey` but no `dataset` is provided.');
      }
      return _extends({}, axisConfig, {
        data: dataset.map(function (d) {
          return d[dataKey];
        })
      });
    });
  }, [inXAxis, dataset]);
  var yAxis = React.useMemo(function () {
    return inYAxis == null ? void 0 : inYAxis.map(function (axisConfig) {
      var dataKey = axisConfig.dataKey;
      if (dataKey === undefined || axisConfig.data !== undefined) {
        return axisConfig;
      }
      if (dataset === undefined) {
        throw Error('MUI-X-Charts: y-axis uses `dataKey` but no `dataset` is provided.');
      }
      return _extends({}, axisConfig, {
        data: dataset.map(function (d) {
          return d[dataKey];
        })
      });
    });
  }, [inYAxis, dataset]);
  var value = React.useMemo(function () {
    var _xAxis$map, _yAxis$map;
    var axisExtremumCallback = function axisExtremumCallback(acc, chartType, axis, getters, isDefaultAxis) {
      var _ref, _formattedSeries$char;
      var getter = getters[chartType];
      var series = (_ref = (_formattedSeries$char = formattedSeries[chartType]) == null ? void 0 : _formattedSeries$char.series) != null ? _ref : {};
      var _getter = getter({
          series: series,
          axis: axis,
          isDefaultAxis: isDefaultAxis
        }),
        _getter2 = _slicedToArray(_getter, 2),
        minChartTypeData = _getter2[0],
        maxChartTypeData = _getter2[1];
      var _acc = _slicedToArray(acc, 2),
        minData = _acc[0],
        maxData = _acc[1];
      if (minData === null || maxData === null) {
        return [minChartTypeData, maxChartTypeData];
      }
      if (minChartTypeData === null || maxChartTypeData === null) {
        return [minData, maxData];
      }
      return [Math.min(minChartTypeData, minData), Math.max(maxChartTypeData, maxData)];
    };
    var getAxisExtremum = function getAxisExtremum(axis, getters, isDefaultAxis) {
      var charTypes = Object.keys(getters);
      return charTypes.reduce(function (acc, charType) {
        return axisExtremumCallback(acc, charType, axis, getters, isDefaultAxis);
      }, [null, null]);
    };
    var allXAxis = [].concat(_toConsumableArray((_xAxis$map = xAxis == null ? void 0 : xAxis.map(function (axis, index) {
      return _extends({
        id: "deaultized-x-axis-".concat(index)
      }, axis);
    })) != null ? _xAxis$map : []), _toConsumableArray(xAxis === undefined || xAxis.findIndex(function (_ref2) {
      var id = _ref2.id;
      return id === DEFAULT_X_AXIS_KEY;
    }) === -1 ? [{
      id: DEFAULT_X_AXIS_KEY,
      scaleType: 'linear'
    }] : []));
    var completedXAxis = {};
    allXAxis.forEach(function (axis, axisIndex) {
      var _axis$scaleType, _axis$min, _axis$max, _axis$min2, _axis$max2;
      var isDefaultAxis = axisIndex === 0;
      var _getAxisExtremum = getAxisExtremum(axis, xExtremumGetters, isDefaultAxis),
        _getAxisExtremum2 = _slicedToArray(_getAxisExtremum, 2),
        minData = _getAxisExtremum2[0],
        maxData = _getAxisExtremum2[1];
      var range = axis.reverse ? [drawingArea.left + drawingArea.width, drawingArea.left] : [drawingArea.left, drawingArea.left + drawingArea.width];
      if (isBandScaleConfig(axis)) {
        var _axis$categoryGapRati, _axis$barGapRatio;
        var categoryGapRatio = (_axis$categoryGapRati = axis.categoryGapRatio) != null ? _axis$categoryGapRati : DEFAULT_CATEGORY_GAP_RATIO;
        var barGapRatio = (_axis$barGapRatio = axis.barGapRatio) != null ? _axis$barGapRatio : DEFAULT_BAR_GAP_RATIO;
        completedXAxis[axis.id] = _extends({
          categoryGapRatio: categoryGapRatio,
          barGapRatio: barGapRatio
        }, axis, {
          scale: scaleBand(axis.data, range).paddingInner(categoryGapRatio).paddingOuter(categoryGapRatio / 2),
          tickNumber: axis.data.length
        });
      }
      if (isPointScaleConfig(axis)) {
        completedXAxis[axis.id] = _extends({}, axis, {
          scale: scalePoint(axis.data, range),
          tickNumber: axis.data.length
        });
      }
      if (axis.scaleType === 'band' || axis.scaleType === 'point') {
        // Could be merged with the two previous "if conditions" but then TS does not get that `axis.scaleType` can't be `band` or `point`.
        return;
      }
      var scaleType = (_axis$scaleType = axis.scaleType) != null ? _axis$scaleType : 'linear';
      var extremums = [(_axis$min = axis.min) != null ? _axis$min : minData, (_axis$max = axis.max) != null ? _axis$max : maxData];
      var tickNumber = getTickNumber(_extends({}, axis, {
        range: range,
        domain: extremums
      }));
      var niceScale = getScale(scaleType, extremums, range).nice(tickNumber);
      var niceDomain = niceScale.domain();
      var domain = [(_axis$min2 = axis.min) != null ? _axis$min2 : niceDomain[0], (_axis$max2 = axis.max) != null ? _axis$max2 : niceDomain[1]];
      completedXAxis[axis.id] = _extends({}, axis, {
        scaleType: scaleType,
        scale: niceScale.domain(domain),
        tickNumber: tickNumber
      });
    });
    var allYAxis = [].concat(_toConsumableArray((_yAxis$map = yAxis == null ? void 0 : yAxis.map(function (axis, index) {
      return _extends({
        id: "deaultized-y-axis-".concat(index)
      }, axis);
    })) != null ? _yAxis$map : []), _toConsumableArray(yAxis === undefined || yAxis.findIndex(function (_ref3) {
      var id = _ref3.id;
      return id === DEFAULT_Y_AXIS_KEY;
    }) === -1 ? [{
      id: DEFAULT_Y_AXIS_KEY,
      scaleType: 'linear'
    }] : []));
    var completedYAxis = {};
    allYAxis.forEach(function (axis, axisIndex) {
      var _axis$scaleType2, _axis$min3, _axis$max3, _axis$min4, _axis$max4;
      var isDefaultAxis = axisIndex === 0;
      var _getAxisExtremum3 = getAxisExtremum(axis, yExtremumGetters, isDefaultAxis),
        _getAxisExtremum4 = _slicedToArray(_getAxisExtremum3, 2),
        minData = _getAxisExtremum4[0],
        maxData = _getAxisExtremum4[1];
      var range = axis.reverse ? [drawingArea.top, drawingArea.top + drawingArea.height] : [drawingArea.top + drawingArea.height, drawingArea.top];
      if (isBandScaleConfig(axis)) {
        var _axis$categoryGapRati2;
        var categoryGapRatio = (_axis$categoryGapRati2 = axis.categoryGapRatio) != null ? _axis$categoryGapRati2 : DEFAULT_CATEGORY_GAP_RATIO;
        completedYAxis[axis.id] = _extends({
          categoryGapRatio: categoryGapRatio,
          barGapRatio: 0
        }, axis, {
          scale: scaleBand(axis.data, [range[1], range[0]]).paddingInner(categoryGapRatio).paddingOuter(categoryGapRatio / 2),
          tickNumber: axis.data.length
        });
      }
      if (isPointScaleConfig(axis)) {
        completedYAxis[axis.id] = _extends({}, axis, {
          scale: scalePoint(axis.data, [range[1], range[0]]),
          tickNumber: axis.data.length
        });
      }
      if (axis.scaleType === 'band' || axis.scaleType === 'point') {
        // Could be merged with the two previous "if conditions" but then TS does not get that `axis.scaleType` can't be `band` or `point`.
        return;
      }
      var scaleType = (_axis$scaleType2 = axis.scaleType) != null ? _axis$scaleType2 : 'linear';
      var extremums = [(_axis$min3 = axis.min) != null ? _axis$min3 : minData, (_axis$max3 = axis.max) != null ? _axis$max3 : maxData];
      var tickNumber = getTickNumber(_extends({}, axis, {
        range: range,
        domain: extremums
      }));
      var niceScale = getScale(scaleType, extremums, range).nice(tickNumber);
      var niceDomain = niceScale.domain();
      var domain = [(_axis$min4 = axis.min) != null ? _axis$min4 : niceDomain[0], (_axis$max4 = axis.max) != null ? _axis$max4 : niceDomain[1]];
      completedYAxis[axis.id] = _extends({}, axis, {
        scaleType: scaleType,
        scale: niceScale.domain(domain),
        tickNumber: tickNumber
      });
    });
    return {
      xAxis: completedXAxis,
      yAxis: completedYAxis,
      xAxisIds: allXAxis.map(function (_ref4) {
        var id = _ref4.id;
        return id;
      }),
      yAxisIds: allYAxis.map(function (_ref5) {
        var id = _ref5.id;
        return id;
      })
    };
  }, [drawingArea.height, drawingArea.left, drawingArea.top, drawingArea.width, formattedSeries, xAxis, yAxis]);

  // @ts-ignore
  return /*#__PURE__*/_jsx(CartesianContext.Provider, {
    value: value,
    children: children
  });
}
process.env.NODE_ENV !== "production" ? CartesianContextProvider.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  children: PropTypes.node,
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: PropTypes.arrayOf(PropTypes.object),
  /**
   * The configuration of the x-axes.
   * If not provided, a default axis config is used with id set to `DEFAULT_X_AXIS_KEY`.
   */
  xAxis: PropTypes.arrayOf(PropTypes.shape({
    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    classes: PropTypes.object,
    data: PropTypes.array,
    dataKey: PropTypes.string,
    disableLine: PropTypes.bool,
    disableTicks: PropTypes.bool,
    fill: PropTypes.string,
    hideTooltip: PropTypes.bool,
    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    label: PropTypes.string,
    labelFontSize: PropTypes.number,
    labelStyle: PropTypes.object,
    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),
    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),
    position: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),
    reverse: PropTypes.bool,
    scaleType: PropTypes.oneOf(['band', 'linear', 'log', 'point', 'pow', 'sqrt', 'time', 'utc']),
    slotProps: PropTypes.object,
    slots: PropTypes.object,
    stroke: PropTypes.string,
    tickFontSize: PropTypes.number,
    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),
    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),
    tickLabelStyle: PropTypes.object,
    tickMaxStep: PropTypes.number,
    tickMinStep: PropTypes.number,
    tickNumber: PropTypes.number,
    tickSize: PropTypes.number,
    valueFormatter: PropTypes.func
  })),
  /**
   * The configuration of the y-axes.
   * If not provided, a default axis config is used with id set to `DEFAULT_Y_AXIS_KEY`.
   */
  yAxis: PropTypes.arrayOf(PropTypes.shape({
    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    classes: PropTypes.object,
    data: PropTypes.array,
    dataKey: PropTypes.string,
    disableLine: PropTypes.bool,
    disableTicks: PropTypes.bool,
    fill: PropTypes.string,
    hideTooltip: PropTypes.bool,
    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    label: PropTypes.string,
    labelFontSize: PropTypes.number,
    labelStyle: PropTypes.object,
    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),
    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),
    position: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),
    reverse: PropTypes.bool,
    scaleType: PropTypes.oneOf(['band', 'linear', 'log', 'point', 'pow', 'sqrt', 'time', 'utc']),
    slotProps: PropTypes.object,
    slots: PropTypes.object,
    stroke: PropTypes.string,
    tickFontSize: PropTypes.number,
    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),
    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),
    tickLabelStyle: PropTypes.object,
    tickMaxStep: PropTypes.number,
    tickMinStep: PropTypes.number,
    tickNumber: PropTypes.number,
    tickSize: PropTypes.number,
    valueFormatter: PropTypes.func
  }))
} : void 0;
export { CartesianContextProvider };