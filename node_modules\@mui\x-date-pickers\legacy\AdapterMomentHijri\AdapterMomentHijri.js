import _extends from "@babel/runtime/helpers/esm/extends";
import _createClass from "@babel/runtime/helpers/esm/createClass";
import _classCallCheck from "@babel/runtime/helpers/esm/classCallCheck";
import _possibleConstructorReturn from "@babel/runtime/helpers/esm/possibleConstructorReturn";
import _getPrototypeOf from "@babel/runtime/helpers/esm/getPrototypeOf";
import _inherits from "@babel/runtime/helpers/esm/inherits";
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
/* eslint-disable class-methods-use-this */
import defaultHMoment from 'moment-hijri';
import { AdapterMoment } from '../AdapterMoment';
// From https://momentjs.com/docs/#/displaying/format/
var formatTokenMap = {
  // Year
  iY: {
    sectionType: 'year',
    contentType: 'letter'
  },
  iYY: {
    sectionType: 'year',
    contentType: 'letter'
  },
  iYYYY: {
    sectionType: 'year',
    contentType: 'letter'
  },
  // Month
  iM: 'month',
  iMM: 'month',
  iMMM: {
    sectionType: 'month',
    contentType: 'letter'
  },
  iMMMM: {
    sectionType: 'month',
    contentType: 'letter'
  },
  // Day of the month
  iD: {
    sectionType: 'day',
    contentType: 'digit',
    maxLength: 2
  },
  iDD: 'day',
  // Meridiem
  A: 'meridiem',
  a: 'meridiem',
  // Hours
  H: {
    sectionType: 'hours',
    contentType: 'digit',
    maxLength: 2
  },
  HH: 'hours',
  h: {
    sectionType: 'hours',
    contentType: 'digit',
    maxLength: 2
  },
  hh: 'hours',
  // Minutes
  m: {
    sectionType: 'minutes',
    contentType: 'digit',
    maxLength: 2
  },
  mm: 'minutes',
  // Seconds
  s: {
    sectionType: 'seconds',
    contentType: 'digit',
    maxLength: 2
  },
  ss: 'seconds'
};
var defaultFormats = {
  year: 'iYYYY',
  month: 'iMMMM',
  monthShort: 'iMMM',
  dayOfMonth: 'iD',
  weekday: 'dddd',
  weekdayShort: 'ddd',
  hours24h: 'HH',
  hours12h: 'hh',
  meridiem: 'A',
  minutes: 'mm',
  seconds: 'ss',
  fullDate: 'iYYYY, iMMMM Do',
  fullDateWithWeekday: 'iYYYY, iMMMM Do, dddd',
  keyboardDateTime: 'iYYYY/iMM/iDD LT',
  shortDate: 'iD iMMM',
  normalDate: 'dddd, iD iMMM',
  normalDateWithWeekday: 'DD iMMMM',
  monthAndYear: 'iMMMM iYYYY',
  monthAndDate: 'iD iMMMM',
  fullTime: 'LT',
  fullTime12h: 'hh:mm A',
  fullTime24h: 'HH:mm',
  fullDateTime: 'iYYYY, iMMMM Do, hh:mm A',
  fullDateTime12h: 'iD iMMMM hh:mm A',
  fullDateTime24h: 'iD iMMMM HH:mm',
  keyboardDate: 'iYYYY/iMM/iDD',
  keyboardDateTime12h: 'iYYYY/iMM/iDD hh:mm A',
  keyboardDateTime24h: 'iYYYY/iMM/iDD HH:mm'
};
var NUMBER_SYMBOL_MAP = {
  '1': '١',
  '2': '٢',
  '3': '٣',
  '4': '٤',
  '5': '٥',
  '6': '٦',
  '7': '٧',
  '8': '٨',
  '9': '٩',
  '0': '٠'
};

/**
 * Based on `@date-io/hijri`
 *
 * MIT License
 *
 * Copyright (c) 2017 Dmitriy Kovalenko
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
export var AdapterMomentHijri = /*#__PURE__*/function (_AdapterMoment) {
  _inherits(AdapterMomentHijri, _AdapterMoment);
  function AdapterMomentHijri() {
    var _this;
    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
      formats = _ref.formats,
      instance = _ref.instance;
    _classCallCheck(this, AdapterMomentHijri);
    _this = _callSuper(this, AdapterMomentHijri, [{
      locale: 'ar-SA',
      instance: instance
    }]);
    _this.lib = 'moment-hijri';
    _this.moment = void 0;
    _this.isTimezoneCompatible = false;
    _this.formatTokenMap = formatTokenMap;
    _this.date = function (value) {
      if (value === null) {
        return null;
      }
      return _this.moment(value).locale('ar-SA');
    };
    _this.dateWithTimezone = function (value) {
      return _this.date(value);
    };
    _this.getTimezone = function () {
      return 'default';
    };
    _this.setTimezone = function (value) {
      return value;
    };
    _this.parse = function (value, format) {
      if (value === '') {
        return null;
      }
      return _this.moment(value, format, true).locale('ar-SA');
    };
    _this.getFormatHelperText = function (format) {
      return _this.expandFormat(format).replace(/a/gi, '(a|p)m').replace('iY', 'Y').replace('iM', 'M').replace('iD', 'D').toLocaleLowerCase();
    };
    _this.formatNumber = function (numberToFormat) {
      return numberToFormat.replace(/\d/g, function (match) {
        return NUMBER_SYMBOL_MAP[match];
      }).replace(/,/g, '،');
    };
    _this.isEqual = function (value, comparing) {
      if (value === null && comparing === null) {
        return true;
      }
      return _this.moment(value).isSame(comparing);
    };
    _this.startOfYear = function (value) {
      return value.clone().startOf('iYear');
    };
    _this.startOfMonth = function (value) {
      return value.clone().startOf('iMonth');
    };
    _this.endOfYear = function (value) {
      return value.clone().endOf('iYear');
    };
    _this.endOfMonth = function (value) {
      return value.clone().endOf('iMonth');
    };
    _this.addYears = function (value, amount) {
      return amount < 0 ? value.clone().subtract(Math.abs(amount), 'iYear') : value.clone().add(amount, 'iYear');
    };
    _this.addMonths = function (value, amount) {
      return amount < 0 ? value.clone().subtract(Math.abs(amount), 'iMonth') : value.clone().add(amount, 'iMonth');
    };
    _this.getYear = function (value) {
      return value.iYear();
    };
    _this.getMonth = function (value) {
      return value.iMonth();
    };
    _this.getDate = function (value) {
      return value.iDate();
    };
    _this.setYear = function (value, year) {
      return value.clone().iYear(year);
    };
    _this.setMonth = function (value, month) {
      return value.clone().iMonth(month);
    };
    _this.setDate = function (value, date) {
      return value.clone().iDate(date);
    };
    _this.getNextMonth = function (value) {
      return value.clone().add(1, 'iMonth');
    };
    _this.getPreviousMonth = function (value) {
      return value.clone().subtract(1, 'iMonth');
    };
    _this.getWeekdays = function () {
      return [0, 1, 2, 3, 4, 5, 6].map(function (dayOfWeek) {
        return _this.date().weekday(dayOfWeek).format('dd');
      });
    };
    _this.getWeekArray = function (value) {
      var start = value.clone().startOf('iMonth').startOf('week');
      var end = value.clone().endOf('iMonth').endOf('week');
      var count = 0;
      var current = start;
      var nestedWeeks = [];
      while (current.isBefore(end)) {
        var weekNumber = Math.floor(count / 7);
        nestedWeeks[weekNumber] = nestedWeeks[weekNumber] || [];
        nestedWeeks[weekNumber].push(current);
        current = current.clone().add(1, 'day');
        count += 1;
      }
      return nestedWeeks;
    };
    _this.getWeekNumber = function (value) {
      return value.iWeek();
    };
    _this.getYearRange = function (start, end) {
      // moment-hijri only supports dates between 1356-01-01 H and 1499-12-29 H
      // We need to throw if outside min/max bounds, otherwise the while loop below will be infinite.
      if (start.isBefore('1937-03-14')) {
        throw new Error('min date must be on or after 1356-01-01 H (1937-03-14)');
      }
      if (end.isAfter('2076-11-26')) {
        throw new Error('max date must be on or before 1499-12-29 H (2076-11-26)');
      }
      var startDate = _this.moment(start).startOf('iYear');
      var endDate = _this.moment(end).endOf('iYear');
      var years = [];
      var current = startDate;
      while (current.isBefore(endDate)) {
        years.push(current);
        current = current.clone().add(1, 'iYear');
      }
      return years;
    };
    _this.getMeridiemText = function (ampm) {
      return ampm === 'am' ? _this.date().hours(2).format('A') : _this.date().hours(14).format('A');
    };
    _this.moment = instance || defaultHMoment;
    _this.locale = 'ar-SA';
    _this.formats = _extends({}, defaultFormats, formats);
    return _this;
  }
  return _createClass(AdapterMomentHijri);
}(AdapterMoment);