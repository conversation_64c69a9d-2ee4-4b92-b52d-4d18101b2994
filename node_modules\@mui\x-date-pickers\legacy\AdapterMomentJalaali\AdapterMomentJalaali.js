import _extends from "@babel/runtime/helpers/esm/extends";
import _createClass from "@babel/runtime/helpers/esm/createClass";
import _classCallCheck from "@babel/runtime/helpers/esm/classCallCheck";
import _possibleConstructorReturn from "@babel/runtime/helpers/esm/possibleConstructorReturn";
import _getPrototypeOf from "@babel/runtime/helpers/esm/getPrototypeOf";
import _inherits from "@babel/runtime/helpers/esm/inherits";
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
/* eslint-disable class-methods-use-this */
import defaultJMoment from 'moment-jalaali';
import { AdapterMoment } from '../AdapterMoment';
// From https://momentjs.com/docs/#/displaying/format/
var formatTokenMap = {
  // Year
  jYY: 'year',
  jYYYY: {
    sectionType: 'year',
    contentType: 'digit',
    maxLength: 4
  },
  // Month
  jM: {
    sectionType: 'month',
    contentType: 'digit',
    maxLength: 2
  },
  jMM: 'month',
  jMMM: {
    sectionType: 'month',
    contentType: 'letter'
  },
  jMMMM: {
    sectionType: 'month',
    contentType: 'letter'
  },
  // Day of the month
  jD: {
    sectionType: 'day',
    contentType: 'digit',
    maxLength: 2
  },
  jDD: 'day',
  // Meridiem
  A: 'meridiem',
  a: 'meridiem',
  // Hours
  H: {
    sectionType: 'hours',
    contentType: 'digit',
    maxLength: 2
  },
  HH: 'hours',
  h: {
    sectionType: 'hours',
    contentType: 'digit',
    maxLength: 2
  },
  hh: 'hours',
  // Minutes
  m: {
    sectionType: 'minutes',
    contentType: 'digit',
    maxLength: 2
  },
  mm: 'minutes',
  // Seconds
  s: {
    sectionType: 'seconds',
    contentType: 'digit',
    maxLength: 2
  },
  ss: 'seconds'
};
var defaultFormats = {
  year: 'jYYYY',
  month: 'jMMMM',
  monthShort: 'jMMM',
  dayOfMonth: 'jD',
  weekday: 'dddd',
  weekdayShort: 'ddd',
  hours24h: 'HH',
  hours12h: 'hh',
  meridiem: 'A',
  minutes: 'mm',
  seconds: 'ss',
  fullDate: 'jYYYY, jMMMM Do',
  fullDateWithWeekday: 'dddd Do jMMMM jYYYY',
  keyboardDate: 'jYYYY/jMM/jDD',
  shortDate: 'jD jMMM',
  normalDate: 'dddd, jD jMMM',
  normalDateWithWeekday: 'DD MMMM',
  monthAndYear: 'jMMMM jYYYY',
  monthAndDate: 'jD jMMMM',
  fullTime: 'LT',
  fullTime12h: 'hh:mm A',
  fullTime24h: 'HH:mm',
  fullDateTime: 'jYYYY, jMMMM Do, hh:mm A',
  fullDateTime12h: 'jD jMMMM hh:mm A',
  fullDateTime24h: 'jD jMMMM HH:mm',
  keyboardDateTime: 'jYYYY/jMM/jDD LT',
  keyboardDateTime12h: 'jYYYY/jMM/jDD hh:mm A',
  keyboardDateTime24h: 'jYYYY/jMM/jDD HH:mm'
};
var NUMBER_SYMBOL_MAP = {
  '1': '۱',
  '2': '۲',
  '3': '۳',
  '4': '۴',
  '5': '۵',
  '6': '۶',
  '7': '۷',
  '8': '۸',
  '9': '۹',
  '0': '۰'
};

/**
 * Based on `@date-io/jalaali`
 *
 * MIT License
 *
 * Copyright (c) 2017 Dmitriy Kovalenko
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
export var AdapterMomentJalaali = /*#__PURE__*/function (_AdapterMoment) {
  _inherits(AdapterMomentJalaali, _AdapterMoment);
  function AdapterMomentJalaali() {
    var _this;
    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
      formats = _ref.formats,
      instance = _ref.instance;
    _classCallCheck(this, AdapterMomentJalaali);
    _this = _callSuper(this, AdapterMomentJalaali, [{
      locale: 'fa',
      instance: instance
    }]);
    _this.isTimezoneCompatible = false;
    _this.lib = 'moment-jalaali';
    _this.moment = void 0;
    _this.formatTokenMap = formatTokenMap;
    _this.date = function (value) {
      if (value === null) {
        return null;
      }
      return _this.moment(value).locale('fa');
    };
    _this.dateWithTimezone = function (value) {
      return _this.date(value);
    };
    _this.getTimezone = function () {
      return 'default';
    };
    _this.setTimezone = function (value) {
      return value;
    };
    _this.parseISO = function (isoString) {
      return _this.moment(isoString).locale('fa');
    };
    _this.parse = function (value, format) {
      if (value === '') {
        return null;
      }
      return _this.moment(value, format, true).locale('fa');
    };
    _this.getFormatHelperText = function (format) {
      return _this.expandFormat(format).replace(/a/gi, '(a|p)m').replace('jY', 'Y').replace('jM', 'M').replace('jD', 'D').toLocaleLowerCase();
    };
    _this.isValid = function (value) {
      // We can't to `this.moment(value)` because moment-jalaali looses the invalidity information when creating a new moment object from an existing one
      if (!_this.moment.isMoment(value)) {
        return false;
      }
      return value.isValid();
    };
    _this.formatNumber = function (numberToFormat) {
      return numberToFormat.replace(/\d/g, function (match) {
        return NUMBER_SYMBOL_MAP[match];
      }).replace(/,/g, '،');
    };
    _this.isEqual = function (value, comparing) {
      if (value === null && comparing === null) {
        return true;
      }
      return _this.moment(value).isSame(comparing);
    };
    _this.isSameYear = function (value, comparing) {
      return value.jYear() === comparing.jYear();
    };
    _this.isSameMonth = function (value, comparing) {
      return value.jYear() === comparing.jYear() && value.jMonth() === comparing.jMonth();
    };
    _this.isAfterYear = function (value, comparing) {
      return value.jYear() > comparing.jYear();
    };
    _this.isBeforeYear = function (value, comparing) {
      return value.jYear() < comparing.jYear();
    };
    _this.startOfYear = function (value) {
      return value.clone().startOf('jYear');
    };
    _this.startOfMonth = function (value) {
      return value.clone().startOf('jMonth');
    };
    _this.endOfYear = function (value) {
      return value.clone().endOf('jYear');
    };
    _this.endOfMonth = function (value) {
      return value.clone().endOf('jMonth');
    };
    _this.addYears = function (value, amount) {
      return amount < 0 ? value.clone().subtract(Math.abs(amount), 'jYear') : value.clone().add(amount, 'jYear');
    };
    _this.addMonths = function (value, amount) {
      return amount < 0 ? value.clone().subtract(Math.abs(amount), 'jMonth') : value.clone().add(amount, 'jMonth');
    };
    _this.getYear = function (value) {
      return value.jYear();
    };
    _this.getMonth = function (value) {
      return value.jMonth();
    };
    _this.getDate = function (value) {
      return value.jDate();
    };
    _this.setYear = function (value, year) {
      return value.clone().jYear(year);
    };
    _this.setMonth = function (value, month) {
      return value.clone().jMonth(month);
    };
    _this.setDate = function (value, date) {
      return value.clone().jDate(date);
    };
    _this.getNextMonth = function (value) {
      return value.clone().add(1, 'jMonth');
    };
    _this.getPreviousMonth = function (value) {
      return value.clone().subtract(1, 'jMonth');
    };
    _this.getWeekdays = function () {
      return [0, 1, 2, 3, 4, 5, 6].map(function (dayOfWeek) {
        return _this.date().weekday(dayOfWeek).format('dd');
      });
    };
    _this.getWeekArray = function (value) {
      var start = value.clone().startOf('jMonth').startOf('week');
      var end = value.clone().endOf('jMonth').endOf('week');
      var count = 0;
      var current = start;
      var nestedWeeks = [];
      while (current.isBefore(end)) {
        var weekNumber = Math.floor(count / 7);
        nestedWeeks[weekNumber] = nestedWeeks[weekNumber] || [];
        nestedWeeks[weekNumber].push(current);
        current = current.clone().add(1, 'day');
        count += 1;
      }
      return nestedWeeks;
    };
    _this.getWeekNumber = function (value) {
      return value.jWeek();
    };
    _this.getYearRange = function (start, end) {
      var startDate = _this.moment(start).startOf('jYear');
      var endDate = _this.moment(end).endOf('jYear');
      var years = [];
      var current = startDate;
      while (current.isBefore(endDate)) {
        years.push(current);
        current = current.clone().add(1, 'jYear');
      }
      return years;
    };
    _this.getMeridiemText = function (ampm) {
      return ampm === 'am' ? _this.date().hours(2).format('A') : _this.date().hours(14).format('A');
    };
    _this.moment = instance || defaultJMoment;
    _this.locale = 'fa';
    _this.formats = _extends({}, defaultFormats, formats);
    return _this;
  }
  return _createClass(AdapterMomentJalaali);
}(AdapterMoment);