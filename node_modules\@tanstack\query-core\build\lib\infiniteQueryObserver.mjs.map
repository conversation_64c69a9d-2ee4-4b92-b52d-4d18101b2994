{"version": 3, "file": "infiniteQueryObserver.mjs", "sources": ["../../src/infiniteQueryObserver.ts"], "sourcesContent": ["import { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type {\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  QueryKey,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions, ObserverFetchOptions } from './queryObserver'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryK<PERSON>,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  InfiniteData<TData>,\n  InfiniteData<TQueryData>,\n  TQueryK<PERSON>\n> {\n  // Type override\n  subscribe!: (\n    listener?: InfiniteQueryObserverListener<TData, TError>,\n  ) => () => void\n\n  // Type override\n  getCurrentResult!: () => InfiniteQueryObserverResult<TData, TError>\n\n  // Type override\n  protected fetch!: (\n    fetchOptions: ObserverFetchOptions,\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options?: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions,\n    )\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage({ pageParam, ...options }: FetchNextPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward', pageParam },\n      },\n    })\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  }: FetchPreviousPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward', pageParam },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, InfiniteData<TQueryData>, TQueryKey>,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const result = super.createResult(query, options)\n\n    const { isFetching, isRefetching } = result\n\n    const isFetchingNextPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'forward'\n\n    const isFetchingPreviousPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'backward'\n\n    return {\n      ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data?.pages),\n      hasPreviousPage: hasPreviousPage(options, state.data?.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n  }\n}\n"], "names": ["InfiniteQueryObserver", "QueryObserver", "constructor", "client", "options", "bindMethods", "fetchNextPage", "bind", "fetchPreviousPage", "setOptions", "notifyOptions", "behavior", "infiniteQueryBehavior", "getOptimisticResult", "pageParam", "fetch", "meta", "fetchMore", "direction", "createResult", "query", "state", "result", "isFetching", "isRefetching", "isFetchingNextPage", "fetchMeta", "isFetchingPreviousPage", "hasNextPage", "data", "pages", "hasPreviousPage"], "mappings": ";;;AAuBO,MAAMA,qBAAN,SAMGC,aANH,CAYL;AACA;AAKA;AAGA;AAKA;AACAC,EAAAA,WAAW,CACTC,MADS,EAETC,OAFS,EAST;IACA,KAAMD,CAAAA,MAAN,EAAcC,OAAd,CAAA,CAAA;AACD,GAAA;;AAESC,EAAAA,WAAW,GAAS;AAC5B,IAAA,KAAA,CAAMA,WAAN,EAAA,CAAA;IACA,IAAKC,CAAAA,aAAL,GAAqB,IAAKA,CAAAA,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArB,CAAA;IACA,IAAKC,CAAAA,iBAAL,GAAyB,IAAKA,CAAAA,iBAAL,CAAuBD,IAAvB,CAA4B,IAA5B,CAAzB,CAAA;AACD,GAAA;;AAEDE,EAAAA,UAAU,CACRL,OADQ,EAQRM,aARQ,EASF;AACN,IAAA,KAAA,CAAMD,UAAN,CACE,EACE,GAAGL,OADL;AAEEO,MAAAA,QAAQ,EAAEC,qBAAqB,EAAA;AAFjC,KADF,EAKEF,aALF,CAAA,CAAA;AAOD,GAAA;;EAEDG,mBAAmB,CACjBT,OADiB,EAQ2B;AAC5CA,IAAAA,OAAO,CAACO,QAAR,GAAmBC,qBAAqB,EAAxC,CAAA;AACA,IAAA,OAAO,KAAMC,CAAAA,mBAAN,CAA0BT,OAA1B,CAAP,CAAA;AAID,GAAA;;AAEDE,EAAAA,aAAa,CAAC;IAAEQ,SAAF;IAAa,GAAGV,OAAAA;AAAhB,GAAA,GAAkD,EAAnD,EAEX;AACA,IAAA,OAAO,IAAKW,CAAAA,KAAL,CAAW,EAChB,GAAGX,OADa;AAEhBY,MAAAA,IAAI,EAAE;AACJC,QAAAA,SAAS,EAAE;AAAEC,UAAAA,SAAS,EAAE,SAAb;AAAwBJ,UAAAA,SAAAA;AAAxB,SAAA;AADP,OAAA;AAFU,KAAX,CAAP,CAAA;AAMD,GAAA;;AAEDN,EAAAA,iBAAiB,CAAC;IAChBM,SADgB;IAEhB,GAAGV,OAAAA;AAFa,GAAA,GAGY,EAHb,EAKf;AACA,IAAA,OAAO,IAAKW,CAAAA,KAAL,CAAW,EAChB,GAAGX,OADa;AAEhBY,MAAAA,IAAI,EAAE;AACJC,QAAAA,SAAS,EAAE;AAAEC,UAAAA,SAAS,EAAE,UAAb;AAAyBJ,UAAAA,SAAAA;AAAzB,SAAA;AADP,OAAA;AAFU,KAAX,CAAP,CAAA;AAMD,GAAA;;AAESK,EAAAA,YAAY,CACpBC,KADoB,EAEpBhB,OAFoB,EASwB;AAAA,IAAA,IAAA,gBAAA,EAAA,qBAAA,EAAA,iBAAA,EAAA,qBAAA,EAAA,WAAA,EAAA,YAAA,CAAA;;IAC5C,MAAM;AAAEiB,MAAAA,KAAAA;AAAF,KAAA,GAAYD,KAAlB,CAAA;IACA,MAAME,MAAM,GAAG,KAAMH,CAAAA,YAAN,CAAmBC,KAAnB,EAA0BhB,OAA1B,CAAf,CAAA;IAEA,MAAM;MAAEmB,UAAF;AAAcC,MAAAA,YAAAA;AAAd,KAAA,GAA+BF,MAArC,CAAA;AAEA,IAAA,MAAMG,kBAAkB,GACtBF,UAAU,IAAI,CAAAF,CAAAA,gBAAAA,GAAAA,KAAK,CAACK,SAAN,KAAiBT,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,gBAAAA,CAAAA,SAAjB,KAA4BC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAAA,CAAAA,SAA5B,MAA0C,SAD1D,CAAA;AAGA,IAAA,MAAMS,sBAAsB,GAC1BJ,UAAU,IAAI,CAAAF,CAAAA,iBAAAA,GAAAA,KAAK,CAACK,SAAN,KAAiBT,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,iBAAAA,CAAAA,SAAjB,KAA4BC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAAA,CAAAA,SAA5B,MAA0C,UAD1D,CAAA;IAGA,OAAO,EACL,GAAGI,MADE;MAELhB,aAAa,EAAE,KAAKA,aAFf;MAGLE,iBAAiB,EAAE,KAAKA,iBAHnB;MAILoB,WAAW,EAAEA,WAAW,CAACxB,OAAD,EAAA,CAAA,WAAA,GAAUiB,KAAK,CAACQ,IAAhB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAU,WAAYC,CAAAA,KAAtB,CAJnB;MAKLC,eAAe,EAAEA,eAAe,CAAC3B,OAAD,EAAA,CAAA,YAAA,GAAUiB,KAAK,CAACQ,IAAhB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAU,YAAYC,CAAAA,KAAtB,CAL3B;MAMLL,kBANK;MAOLE,sBAPK;AAQLH,MAAAA,YAAY,EACVA,YAAY,IAAI,CAACC,kBAAjB,IAAuC,CAACE,sBAAAA;KAT5C,CAAA;AAWD,GAAA;;AA9HD;;;;"}