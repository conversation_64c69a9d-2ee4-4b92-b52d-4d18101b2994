{"version": 3, "file": "index.production.js", "sources": ["../../src/subscribable.ts", "../../src/utils.ts", "../../src/focusManager.ts", "../../src/onlineManager.ts", "../../src/retryer.ts", "../../src/logger.ts", "../../src/notifyManager.ts", "../../src/removable.ts", "../../src/query.ts", "../../src/queryCache.ts", "../../src/mutation.ts", "../../src/mutationCache.ts", "../../src/infiniteQueryBehavior.ts", "../../src/queryObserver.ts", "../../src/hydration.ts", "../../src/infiniteQueryObserver.ts", "../../src/mutationObserver.ts", "../../src/queriesObserver.ts", "../../src/queryClient.ts"], "sourcesContent": ["type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: Set<{ listener: TListener }>\n\n  constructor() {\n    this.listeners = new Set()\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    const identity = { listener }\n    this.listeners.add(identity)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(identity)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n", "import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport type {\n  FetchStatus,\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in window\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>,\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1) ? [{ ...arg2, queryKey: arg1 }, arg3] : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs<\n  TFilters extends MutationFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1)\n      ? [{ ...arg2, mutationKey: arg1 }, arg3]\n      : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (\n    typeof fetchStatus !== 'undefined' &&\n    fetchStatus !== query.state.fetchStatus\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(a, b)\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some((key) => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: unknown): value is QueryKey {\n  return Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void) {\n  sleep(0).then(callback)\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n  return\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual?.(prevData, data)) {\n    return prevData as TData\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data)\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable {\n  private focused?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibillitychange and focus\n        window.addEventListener('visibilitychange', listener, false)\n        window.addEventListener('focus', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n          window.removeEventListener('focus', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.focused !== focused\n    if (changed) {\n      this.focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.focused === 'boolean') {\n      return this.focused\n    }\n\n    // document global can be unavailable in react native\n    if (typeof document === 'undefined') {\n      return true\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(\n      document.visibilityState,\n    )\n  }\n}\n\nexport const focusManager = new FocusManager()\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setOnline: (online?: boolean) => void,\n) => (() => void) | undefined\n\nconst onlineEvents = ['online', 'offline'] as const\n\nexport class OnlineManager extends Subscribable {\n  private online?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline()\n        // Listen to online\n        onlineEvents.forEach((event) => {\n          window.addEventListener(event, listener, false)\n        })\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach((event) => {\n            window.removeEventListener(event, listener)\n          })\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((online?: boolean) => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online)\n      } else {\n        this.onOnline()\n      }\n    })\n  }\n\n  setOnline(online?: boolean): void {\n    const changed = this.online !== online\n\n    if (changed) {\n      this.online = online\n      this.onOnline()\n    }\n  }\n\n  onOnline(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isOnline(): boolean {\n    if (typeof this.online === 'boolean') {\n      return this.online\n    }\n\n    if (\n      typeof navigator === 'undefined' ||\n      typeof navigator.onLine === 'undefined'\n    ) {\n      return true\n    }\n\n    return navigator.onLine\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n", "import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { sleep } from './utils'\nimport type { CancelOptions, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = unknown> {\n  fn: () => TData | Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = unknown>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => boolean) | undefined\n  let promiseResolve: (data: TData) => void\n  let promiseReject: (error: TError) => void\n\n  const promise = new Promise<TData>((outerResolve, outerReject) => {\n    promiseResolve = outerResolve\n    promiseReject = outerReject\n  })\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const shouldPause = () =>\n    !focusManager.isFocused() ||\n    (config.networkMode !== 'always' && !onlineManager.isOnline())\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      promiseResolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      promiseReject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        const canContinue = isResolved || !shouldPause()\n        if (canContinue) {\n          continueResolve(value)\n        }\n        return canContinue\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // Execute query\n    try {\n      promiseOrValue = config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? 3\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            if (shouldPause()) {\n              return pause()\n            }\n            return\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  // Start loop\n  if (canFetch(config.networkMode)) {\n    run()\n  } else {\n    pause().then(run)\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn?.()\n      return didContinue ? promise : Promise.resolve()\n    },\n    cancelRetry,\n    continueRetry,\n  }\n}\n", "export interface Logger {\n  log: LogFunction\n  warn: LogFunction\n  error: LogFunction\n}\n\ntype LogFunction = (...args: any[]) => void\n\nexport const defaultLogger: Logger = console\n", "import { scheduleMicrotask } from './utils'\n\n// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends unknown[]> = (...args: T) => void\n\nexport function createNotifyManager() {\n  let queue: NotifyCallback[] = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n\n  const batch = <T>(callback: () => T): T => {\n    let result\n    transactions++\n    try {\n      result = callback()\n    } finally {\n      transactions--\n      if (!transactions) {\n        flush()\n      }\n    }\n    return result\n  }\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  const batchCalls = <T extends unknown[]>(\n    callback: BatchCallsCallback<T>,\n  ): BatchCallsCallback<T> => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args)\n      })\n    }\n  }\n\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  const setNotifyFunction = (fn: NotifyFunction) => {\n    notifyFn = fn\n  }\n\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  const setBatchNotifyFunction = (fn: BatchNotifyFunction) => {\n    batchNotifyFn = fn\n  }\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction,\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n", "import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  cacheTime!: number\n  private gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  protected updateCacheTime(newCacheTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      newCacheTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout)\n      this.gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n", "import { getAbortController, noop, replaceData, timeUntilStale } from './utils'\nimport { defaultLogger } from './logger'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type {\n  CancelOptions,\n  FetchStatus,\n  InitialDataFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport type { Logger } from './logger'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  logger?: Logger\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: any\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal?: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  isFetchingOptimistic?: boolean\n\n  private cache: QueryCache\n  private logger: Logger\n  private promise?: Promise<TData>\n  private retryer?: Retryer<TData>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.abortSignalConsumed = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.logger = config.logger || defaultLogger\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || getDefaultState(this.options)\n    this.state = this.initialState\n    this.scheduleGc()\n  }\n\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some((observer) => observer.options.enabled !== false)\n  }\n\n  isDisabled(): boolean {\n    return this.getObserversCount() > 0 && !this.isActive()\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some((observer) => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true\n            return abortController.signal\n          }\n          return undefined\n        },\n      })\n    }\n\n    addSignalProperty(queryFnContext)\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\n          `Missing queryFn for queryKey '${this.options.queryHash}'`,\n        )\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(context)\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      }\n      this.isFetchingOptimistic = false\n    }\n\n    // Try to fetch the data\n    this.retryer = createRetryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc()\n        }\n        this.isFetchingOptimistic = false\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: action.meta ?? null,\n            fetchStatus: canFetch(this.options.networkMode)\n              ? 'fetching'\n              : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading',\n            }),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error as unknown\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error: error as TError,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error as TError,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = typeof data !== 'undefined'\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle',\n  }\n}\n", "import { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type { NotifyEvent, OmitKeyof, QueryKey, QueryOptions } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (error: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface QueryHashMap {\n  [hash: string]: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  config: QueryCacheConfig\n\n  private queries: Query<any, any, any, any>[]\n  private queriesMap: QueryHashMap\n\n  constructor(config?: QueryCacheConfig) {\n    super()\n    this.config = config || {}\n    this.queries = []\n    this.queriesMap = {}\n  }\n\n  build<TQueryFnData, TError, TData, TQueryKey extends QueryKey>(\n    client: QueryClient,\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey!\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query\n      this.queries.push(query)\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.queriesMap[query.queryHash]\n\n    if (queryInMap) {\n      query.destroy()\n\n      this.queries = this.queries.filter((x) => x !== query)\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash]\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.queriesMap[queryHash]\n  }\n\n  getAll(): Query[] {\n    return this.queries\n  }\n\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    filters: QueryFilters,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const [filters] = parseFilterArgs(arg1, arg2)\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.queries.find((query) => matchQuery(filters, query))\n  }\n\n  findAll(filters?: QueryFilters): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[] {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    return Object.keys(filters).length > 0\n      ? this.queries.filter((query) => matchQuery(filters, query))\n      : this.queries\n  }\n\n  notify(event: QueryCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n", "import { defaultLogger } from './logger'\nimport { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { canFetch, createRetry<PERSON> } from './retryer'\nimport type { MutationMeta, MutationOptions, MutationStatus } from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { Logger } from './logger'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  logger?: Logger\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n  meta?: MutationMeta\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface LoadingAction<TVariables, TContext> {\n  type: 'loading'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError, TVariables, TContext> {\n  type: 'setState'\n  state: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | LoadingAction<TVariables, TContext>\n  | PauseAction\n  | SetStateAction<TData, TError, TVariables, TContext>\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  mutationId: number\n\n  private observers: MutationObserver<TData, TError, TVariables, TContext>[]\n  private defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  private mutationCache: MutationCache\n  private logger: Logger\n  private retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.defaultOptions = config.defaultOptions\n    this.mutationId = config.mutationId\n    this.mutationCache = config.mutationCache\n    this.logger = config.logger || defaultLogger\n    this.observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options?: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  setState(state: MutationState<TData, TError, TVariables, TContext>): void {\n    this.dispatch({ type: 'setState', state })\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.observers = this.observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc()\n      } else {\n        this.mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return this.retryer?.continue() ?? this.execute()\n  }\n\n  async execute(): Promise<TData> {\n    const executeMutation = () => {\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found')\n          }\n          return this.options.mutationFn(this.state.variables!)\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({ type: 'failed', failureCount, error })\n        },\n        onPause: () => {\n          this.dispatch({ type: 'pause' })\n        },\n        onContinue: () => {\n          this.dispatch({ type: 'continue' })\n        },\n        retry: this.options.retry ?? 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode,\n      })\n\n      return this.retryer.promise\n    }\n\n    const restored = this.state.status === 'loading'\n    try {\n      if (!restored) {\n        this.dispatch({ type: 'loading', variables: this.options.variables! })\n        // Notify cache callback\n        await this.mutationCache.config.onMutate?.(\n          this.state.variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(this.state.variables!)\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables,\n          })\n        }\n      }\n      const data = await executeMutation()\n\n      // Notify cache callback\n      await this.mutationCache.config.onSuccess?.(\n        data,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(\n        data,\n        this.state.variables!,\n        this.state.context!,\n      )\n\n      // Notify cache callback\n      await this.mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(\n        data,\n        null,\n        this.state.variables!,\n        this.state.context,\n      )\n\n      this.dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.mutationCache.config.onError?.(\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n\n        await this.options.onError?.(\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.mutationCache.config.onSettled?.(\n          undefined,\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.dispatch({ type: 'error', error: error as TError })\n      }\n    }\n  }\n\n  private dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'loading':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n  }\n}\n", "import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\ntype MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  config: MutationCacheConfig\n\n  private mutations: Mutation<any, any, any, any>[]\n  private mutationId: number\n  private resuming: Promise<unknown> | undefined\n\n  constructor(config?: MutationCacheConfig) {\n    super()\n    this.config = config || {}\n    this.mutations = []\n    this.mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey\n        ? client.getMutationDefaults(options.mutationKey)\n        : undefined,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.mutations.push(mutation)\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    this.mutations = this.mutations.filter((x) => x !== mutation)\n    this.notify({ type: 'removed', mutation })\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.mutations.forEach((mutation) => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Mutation[] {\n    return this.mutations\n  }\n\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.mutations.find((mutation) => matchMutation(filters, mutation))\n  }\n\n  findAll(filters: MutationFilters): Mutation[] {\n    return this.mutations.filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    this.resuming = (this.resuming ?? Promise.resolve())\n      .then(() => {\n        const pausedMutations = this.mutations.filter((x) => x.state.isPaused)\n        return notifyManager.batch(() =>\n          pausedMutations.reduce(\n            (promise, mutation) =>\n              promise.then(() => mutation.continue().catch(noop)),\n            Promise.resolve() as Promise<unknown>,\n          ),\n        )\n      })\n      .then(() => {\n        this.resuming = undefined\n      })\n\n    return this.resuming\n  }\n}\n", "import type { QueryBehavior } from './query'\n\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData,\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: (context) => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal?.aborted) {\n                cancelled = true\n              } else {\n                context.signal?.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn ||\n          (() =>\n            Promise.reject(\n              `Missing queryFn for queryKey '${context.options.queryHash}'`,\n            ))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean,\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean,\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then((page) =>\n            buildNewPages(pages, param, page, previous),\n          )\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then((pages) => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i]),\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then((pages) => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n  return\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n  return\n}\n", "import {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { canFetch, isCancelledError } from './retryer'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\nimport type { Action, FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { DefaultedQueryObserverOptions, RefetchPageFilters } from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: TError | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: ReturnType<typeof setTimeout>\n  private refetchIntervalId?: ReturnType<typeof setInterval>\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Set<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = new Set()\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryOptions(options)\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof options?.isDataEqual !== 'undefined'\n    ) {\n      this.client\n        .getLogger()\n        .error(\n          `The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option`,\n        )\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this,\n      })\n    }\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.client.getQueryCache().build(this.client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result\n      this.currentResultOptions = this.options\n      this.currentResultState = this.currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    return trackedResult\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>({\n    refetchPage,\n    ...options\n  }: RefetchOptions & RefetchPageFilters<TPageData> = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(this.client, defaultedOptions)\n    query.isFetchingOptimistic = true\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime,\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearStaleTimeout(): void {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId)\n      this.staleTimeoutId = undefined\n    }\n  }\n\n  private clearRefetchInterval(): void {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId)\n      this.refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, fetchStatus, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode)\n          ? 'fetching'\n          : 'paused'\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle'\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdatedAt &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          data = replaceData(prevResult?.data, data, options)\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError)\n          }\n          this.selectError = selectError as TError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = state.data as unknown as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      status === 'loading'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            this.selectError = null\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError)\n            }\n            this.selectError = selectError as TError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = replaceData(prevResult?.data, placeholderData, options) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = fetchStatus === 'fetching'\n    const isLoading = status === 'loading'\n    const isError = status === 'error'\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.currentResult = nextResult\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.trackedProps,\n      )\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey]\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client.getQueryCache().build(this.client, this.options)\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated',\n        })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect'],\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false\n  }\n\n  // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData\n  }\n\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n", "import type { QueryClient } from './queryClient'\nimport type { Query, QueryState } from './query'\nimport type {\n  MutationKey,\n  MutationOptions,\n  QueryKey,\n  QueryOptions,\n} from './types'\nimport type { Mutation, MutationState } from './mutation'\n\n// TYPES\n\nexport interface DehydrateOptions {\n  dehydrateMutations?: boolean\n  dehydrateQueries?: boolean\n  shouldDehydrateMutation?: ShouldDehydrateMutationFunction\n  shouldDehydrateQuery?: ShouldDehydrateQueryFunction\n}\n\nexport interface HydrateOptions {\n  defaultOptions?: {\n    queries?: QueryOptions\n    mutations?: MutationOptions\n  }\n}\n\ninterface DehydratedMutation {\n  mutationKey?: MutationKey\n  state: MutationState\n}\n\ninterface DehydratedQuery {\n  queryHash: string\n  queryKey: QueryKey\n  state: QueryState\n}\n\nexport interface DehydratedState {\n  mutations: DehydratedMutation[]\n  queries: DehydratedQuery[]\n}\n\nexport type ShouldDehydrateQueryFunction = (query: Query) => boolean\n\nexport type ShouldDehydrateMutationFunction = (mutation: Mutation) => boolean\n\n// FUNCTIONS\n\nfunction dehydrateMutation(mutation: Mutation): DehydratedMutation {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n  }\n}\n\n// Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\nfunction dehydrateQuery(query: Query): DehydratedQuery {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n  }\n}\n\nexport function defaultShouldDehydrateMutation(mutation: Mutation) {\n  return mutation.state.isPaused\n}\n\nexport function defaultShouldDehydrateQuery(query: Query) {\n  return query.state.status === 'success'\n}\n\nexport function dehydrate(\n  client: QueryClient,\n  options: DehydrateOptions = {},\n): DehydratedState {\n  const mutations: DehydratedMutation[] = []\n  const queries: DehydratedQuery[] = []\n\n  if (options.dehydrateMutations !== false) {\n    const shouldDehydrateMutation =\n      options.shouldDehydrateMutation || defaultShouldDehydrateMutation\n\n    client\n      .getMutationCache()\n      .getAll()\n      .forEach((mutation) => {\n        if (shouldDehydrateMutation(mutation)) {\n          mutations.push(dehydrateMutation(mutation))\n        }\n      })\n  }\n\n  if (options.dehydrateQueries !== false) {\n    const shouldDehydrateQuery =\n      options.shouldDehydrateQuery || defaultShouldDehydrateQuery\n\n    client\n      .getQueryCache()\n      .getAll()\n      .forEach((query) => {\n        if (shouldDehydrateQuery(query)) {\n          queries.push(dehydrateQuery(query))\n        }\n      })\n  }\n\n  return { mutations, queries }\n}\n\nexport function hydrate(\n  client: QueryClient,\n  dehydratedState: unknown,\n  options?: HydrateOptions,\n): void {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return\n  }\n\n  const mutationCache = client.getMutationCache()\n  const queryCache = client.getQueryCache()\n\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const mutations = (dehydratedState as DehydratedState).mutations || []\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const queries = (dehydratedState as DehydratedState).queries || []\n\n  mutations.forEach((dehydratedMutation) => {\n    mutationCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.mutations,\n        mutationKey: dehydratedMutation.mutationKey,\n      },\n      dehydratedMutation.state,\n    )\n  })\n\n  queries.forEach(({ queryKey, state, queryHash }) => {\n    const query = queryCache.get(queryHash)\n\n    // Do not hydrate if an existing query exists with newer data\n    if (query) {\n      if (query.state.dataUpdatedAt < state.dataUpdatedAt) {\n        // omit fetchStatus from dehydrated state\n        // so that query stays in its current fetchStatus\n        const { fetchStatus: _ignored, ...dehydratedQueryState } = state\n        query.setState(dehydratedQueryState)\n      }\n      return\n    }\n\n    // Restore query\n    queryCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.queries,\n        queryKey,\n        queryHash,\n      },\n      // Reset fetch status to idle to avoid\n      // query being stuck in fetching state upon hydration\n      {\n        ...state,\n        fetchStatus: 'idle',\n      },\n    )\n  })\n}\n", "import { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type {\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  QueryKey,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions, ObserverFetchOptions } from './queryObserver'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryK<PERSON>,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  InfiniteData<TData>,\n  InfiniteData<TQueryData>,\n  TQueryK<PERSON>\n> {\n  // Type override\n  subscribe!: (\n    listener?: InfiniteQueryObserverListener<TData, TError>,\n  ) => () => void\n\n  // Type override\n  getCurrentResult!: () => InfiniteQueryObserverResult<TData, TError>\n\n  // Type override\n  protected fetch!: (\n    fetchOptions: ObserverFetchOptions,\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options?: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions,\n    )\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage({ pageParam, ...options }: FetchNextPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward', pageParam },\n      },\n    })\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  }: FetchPreviousPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward', pageParam },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, InfiniteData<TQueryData>, TQueryKey>,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const result = super.createResult(query, options)\n\n    const { isFetching, isRefetching } = result\n\n    const isFetchingNextPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'forward'\n\n    const isFetchingPreviousPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'backward'\n\n    return {\n      ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data?.pages),\n      hasPreviousPage: hasPreviousPage(options, state.data?.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n  }\n}\n", "import { getDefaultState } from './mutation'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options\n    this.options = this.client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this,\n      })\n    }\n    this.currentMutation?.setOptions(this.options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const isLoading = state.status === 'loading'\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading,\n      isPending: isLoading,\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!,\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n", "import { difference, replaceAt } from './utils'\nimport { notifyManager } from './notifyManager'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\nimport type {\n  DefaultedQueryObserverOptions,\n  QueryObserverOptions,\n  QueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\n\ntype QueriesObserverListener = (result: QueryObserverResult[]) => void\n\nexport class QueriesObserver extends Subscribable<QueriesObserverListener> {\n  private client: QueryClient\n  private result: QueryObserverResult[]\n  private queries: QueryObserverOptions[]\n  private observers: QueryObserver[]\n  private observersMap: Record<string, QueryObserver>\n\n  constructor(client: QueryClient, queries?: QueryObserverOptions[]) {\n    super()\n\n    this.client = client\n    this.queries = []\n    this.result = []\n    this.observers = []\n    this.observersMap = {}\n\n    if (queries) {\n      this.setQueries(queries)\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: QueryObserverOptions[],\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.queries = queries\n\n    notifyManager.batch(() => {\n      const prevObservers = this.observers\n\n      const newObserverMatches = this.findMatchingObservers(this.queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newObserversMap = Object.fromEntries(\n        newObservers.map((observer) => [observer.options.queryHash, observer]),\n      )\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.observers = newObservers\n      this.observersMap = newObserversMap\n      this.result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n\n      this.notify()\n    })\n  }\n\n  getCurrentResult(): QueryObserverResult[] {\n    return this.result\n  }\n\n  getQueries() {\n    return this.observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.observers\n  }\n\n  getOptimisticResult(queries: QueryObserverOptions[]): QueryObserverResult[] {\n    return this.findMatchingObservers(queries).map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n  }\n\n  private findMatchingObservers(\n    queries: QueryObserverOptions[],\n  ): QueryObserverMatch[] {\n    const prevObservers = this.observers\n    const prevObserversMap = new Map(\n      prevObservers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const defaultedQueryOptions = queries.map((options) =>\n      this.client.defaultQueryOptions(options),\n    )\n\n    const matchingObservers: QueryObserverMatch[] =\n      defaultedQueryOptions.flatMap((defaultedOptions) => {\n        const match = prevObserversMap.get(defaultedOptions.queryHash)\n        if (match != null) {\n          return [{ defaultedQueryOptions: defaultedOptions, observer: match }]\n        }\n        return []\n      })\n\n    const matchedQueryHashes = new Set(\n      matchingObservers.map((match) => match.defaultedQueryOptions.queryHash),\n    )\n    const unmatchedQueries = defaultedQueryOptions.filter(\n      (defaultedOptions) => !matchedQueryHashes.has(defaultedOptions.queryHash),\n    )\n\n    const matchingObserversSet = new Set(\n      matchingObservers.map((match) => match.observer),\n    )\n    const unmatchedObservers = prevObservers.filter(\n      (prevObserver) => !matchingObserversSet.has(prevObserver),\n    )\n\n    const getObserver = (options: QueryObserverOptions): QueryObserver => {\n      const defaultedOptions = this.client.defaultQueryOptions(options)\n      const currentObserver = this.observersMap[defaultedOptions.queryHash!]\n      return currentObserver ?? new QueryObserver(this.client, defaultedOptions)\n    }\n\n    const newOrReusedObservers: QueryObserverMatch[] = unmatchedQueries.map(\n      (options, index) => {\n        if (options.keepPreviousData) {\n          // return previous data from one of the observers that no longer match\n          const previouslyUsedObserver = unmatchedObservers[index]\n          if (previouslyUsedObserver !== undefined) {\n            return {\n              defaultedQueryOptions: options,\n              observer: previouslyUsedObserver,\n            }\n          }\n        }\n        return {\n          defaultedQueryOptions: options,\n          observer: getObserver(options),\n        }\n      },\n    )\n\n    const sortMatchesByOrderOfQueries = (\n      a: QueryObserverMatch,\n      b: QueryObserverMatch,\n    ): number =>\n      defaultedQueryOptions.indexOf(a.defaultedQueryOptions) -\n      defaultedQueryOptions.indexOf(b.defaultedQueryOptions)\n\n    return matchingObservers\n      .concat(newOrReusedObservers)\n      .sort(sortMatchesByOrderOfQueries)\n  }\n\n  private onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result)\n      this.notify()\n    }\n  }\n\n  private notify(): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(this.result)\n      })\n    })\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n", "import {\n  functionalUpdate,\n  hashQueryKey,\n  hashQueryKeyByOptions,\n  noop,\n  parseFilterArgs,\n  parseQueryArgs,\n  partialMatchKey,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport { defaultLogger } from './logger'\nimport type { OmitKeyof } from '@tanstack/query-core'\nimport type { CancelOptions, DefaultedQueryObserverOptions } from './types'\nimport type { Logger } from './logger'\nimport type { QueryState } from './query'\nimport type {\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  QueryClientConfig,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n  WithRequired,\n} from './types'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: QueryOptions<any, any, any>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  private queryCache: QueryCache\n  private mutationCache: MutationCache\n  private logger: Logger\n  private defaultOptions: DefaultOptions\n  private queryDefaults: QueryDefaults[]\n  private mutationDefaults: MutationDefaults[]\n  private mountCount: number\n  private unsubscribeFocus?: () => void\n  private unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.queryCache = config.queryCache || new QueryCache()\n    this.mutationCache = config.mutationCache || new MutationCache()\n    this.logger = config.logger || defaultLogger\n    this.defaultOptions = config.defaultOptions || {}\n    this.queryDefaults = []\n    this.mutationDefaults = []\n    this.mountCount = 0\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\n        `Passing a custom logger has been deprecated and will be removed in the next major version.`,\n      )\n    }\n  }\n\n  mount(): void {\n    this.mountCount++\n    if (this.mountCount !== 1) return\n\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations()\n        this.queryCache.onFocus()\n      }\n    })\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations()\n        this.queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.mountCount--\n    if (this.mountCount !== 0) return\n\n    this.unsubscribeFocus?.()\n    this.unsubscribeFocus = undefined\n\n    this.unsubscribeOnline?.()\n    this.unsubscribeOnline = undefined\n  }\n\n  isFetching(filters?: QueryFilters): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): number {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    filters.fetchStatus = 'fetching'\n    return this.queryCache.findAll(filters).length\n  }\n\n  isMutating(filters?: MutationFilters): number {\n    return this.mutationCache.findAll({ ...filters, fetching: true }).length\n  }\n\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined {\n    return this.queryCache.find<TQueryFnData>(queryKey, filters)?.state.data\n  }\n\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: WithRequired<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | WithRequired<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const cachedData = this.getQueryData<TData>(parsedOptions.queryKey!)\n\n    return cachedData\n      ? Promise.resolve(cachedData)\n      : this.fetchQuery(parsedOptions)\n  }\n\n  getQueriesData<TQueryFnData = unknown>(\n    filters: QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return this.getQueryCache()\n      .findAll(queryKeyOrFilters)\n      .map(({ queryKey, state }) => {\n        const data = state.data as TQueryFnData | undefined\n        return [queryKey, data]\n      })\n  }\n\n  setQueryData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): TQueryFnData | undefined {\n    const query = this.queryCache.find<TQueryFnData>(queryKey)\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (typeof data === 'undefined') {\n      return undefined\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n    return this.queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<TQueryFnData>(\n    filters: QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return notifyManager.batch(() =>\n      this.getQueryCache()\n        .findAll(queryKeyOrFilters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<TQueryFnData = unknown, TError = undefined>(\n    queryKey: QueryKey,\n    /**\n     * @deprecated This filters will be removed in the next major version.\n     */\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): QueryState<TQueryFnData, TError> | undefined {\n    return this.queryCache.find<TQueryFnData, TError>(queryKey, filters)?.state\n  }\n\n  removeQueries(filters?: QueryFilters): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    const queryCache = this.queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<ResetQueryFilters<TPageData>, 'queryKey'>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(\n    arg1?: QueryKey | ResetQueryFilters,\n    arg2?: OmitKeyof<ResetQueryFilters, 'queryKey'> | ResetOptions,\n    arg3?: ResetOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n    const queryCache = this.queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      type: 'active',\n      ...filters,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries(filters?: QueryFilters, options?: CancelOptions): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n    options?: CancelOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'> | CancelOptions,\n    arg3?: CancelOptions,\n  ): Promise<void> {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3)\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true\n    }\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(cancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<InvalidateQueryFilters<TPageData>, 'queryKey'>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(\n    arg1?: QueryKey | InvalidateQueryFilters,\n    arg2?: OmitKeyof<InvalidateQueryFilters, 'queryKey'> | InvalidateOptions,\n    arg3?: InvalidateOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    return notifyManager.batch(() => {\n      this.queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      const refetchFilters: RefetchQueryFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? 'active',\n      }\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<RefetchQueryFilters<TPageData>, 'queryKey'>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(\n    arg1?: QueryKey | RefetchQueryFilters,\n    arg2?: OmitKeyof<RefetchQueryFilters, 'queryKey'> | RefetchOptions,\n    arg3?: RefetchOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled())\n        .map((query) =>\n          query.fetch(undefined, {\n            ...options,\n            cancelRefetch: options?.cancelRefetch ?? true,\n            meta: { refetchPage: filters.refetchPage },\n          }),\n        ),\n    )\n\n    let promise = Promise.all(promises).then(noop)\n\n    if (!options?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(defaultedOptions.staleTime)\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    parsedOptions.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData\n    >()\n    return this.fetchQuery(parsedOptions)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    return this.mutationCache.resumePausedMutations()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.mutationCache\n  }\n\n  getLogger(): Logger {\n    return this.logger\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.defaultOptions = options\n  }\n\n  setQueryDefaults(\n    queryKey: QueryKey,\n    options: QueryObserverOptions<unknown, any, any, any>,\n  ): void {\n    const result = this.queryDefaults.find(\n      (x) => hashQueryKey(queryKey) === hashQueryKey(x.queryKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.queryDefaults.push({ queryKey, defaultOptions: options })\n    }\n  }\n\n  getQueryDefaults(\n    queryKey?: QueryKey,\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    if (!queryKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.queryDefaults.find((x) =>\n      partialMatchKey(queryKey, x.queryKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter((x) =>\n        partialMatchKey(queryKey, x.queryKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several query defaults match with key '${JSON.stringify(\n            queryKey,\n          )}'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  setMutationDefaults(\n    mutationKey: MutationKey,\n    options: MutationObserverOptions<any, any, any, any>,\n  ): void {\n    const result = this.mutationDefaults.find(\n      (x) => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.mutationDefaults.push({ mutationKey, defaultOptions: options })\n    }\n  }\n\n  getMutationDefaults(\n    mutationKey?: MutationKey,\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    if (!mutationKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.mutationDefaults.find((x) =>\n      partialMatchKey(mutationKey, x.mutationKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter((x) =>\n        partialMatchKey(mutationKey, x.mutationKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several mutation defaults match with key '${JSON.stringify(\n            mutationKey,\n          )}'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  defaultQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey,\n  >(\n    options?:\n      | QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options?._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options?.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options?.mutationKey),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.queryCache.clear()\n    this.mutationCache.clear()\n  }\n}\n"], "names": ["Subscribable", "constructor", "this", "listeners", "Set", "subscribe", "bind", "listener", "identity", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size", "isServer", "window", "noop", "isValidTimeout", "value", "Infinity", "difference", "array1", "array2", "filter", "x", "includes", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "parseFilter<PERSON><PERSON>s", "matchQuery", "filters", "query", "type", "exact", "fetchStatus", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "fetching", "<PERSON><PERSON><PERSON>", "hashQuery<PERSON>ey", "status", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "partialDeepEqual", "some", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aSize", "length", "bItems", "bSize", "copy", "equalItems", "i", "shallowEqualObjects", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "prot", "prototype", "hasOwnProperty", "toString", "call", "sleep", "timeout", "Promise", "resolve", "setTimeout", "scheduleMicrotask", "callback", "then", "replaceData", "prevData", "data", "isDataEqual", "structuralSharing", "focusManager", "super", "setup", "onFocus", "addEventListener", "removeEventListener", "cleanup", "setEventListener", "_this$cleanup", "undefined", "_this$cleanup2", "focused", "setFocused", "for<PERSON>ach", "isFocused", "document", "visibilityState", "onlineEvents", "onlineManager", "onOnline", "event", "online", "setOnline", "isOnline", "navigator", "onLine", "defaultRetryDelay", "failureCount", "min", "canFetch", "networkMode", "CancelledError", "revert", "silent", "isCancelledError", "createRetryer", "config", "continueFn", "promiseResolve", "promiseReject", "isRetryCancelled", "isResolved", "promise", "outerResolve", "outerReject", "shouldP<PERSON>e", "onSuccess", "reject", "onError", "pause", "continueResolve", "canContinue", "onPause", "onContinue", "run", "promiseOrValue", "fn", "error", "catch", "_config$retry", "_config$retryDelay", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "cancel", "cancelOptions", "abort", "continue", "cancelRetry", "continueRetry", "defaultLogger", "console", "notify<PERSON><PERSON>ger", "queue", "transactions", "notifyFn", "batchNotifyFn", "schedule", "push", "flush", "originalQueue", "batch", "batchCalls", "args", "setNotifyFunction", "setBatchNotifyFunction", "createNotifyManager", "Removable", "destroy", "clearGcTimeout", "scheduleGc", "cacheTime", "gcTimeout", "optionalRemove", "updateCacheTime", "newCacheTime", "clearTimeout", "Query", "abortSignalConsumed", "defaultOptions", "setOptions", "observers", "cache", "logger", "initialState", "initialData", "hasData", "initialDataUpdatedAt", "dataUpdateCount", "dataUpdatedAt", "errorUpdateCount", "errorUpdatedAt", "fetchFailureCount", "fetchFailureReason", "fetchMeta", "isInvalidated", "getDefaultState", "meta", "remove", "setData", "newData", "dispatch", "manual", "setState", "setStateOptions", "_this$retryer", "retryer", "reset", "observer", "enabled", "isDisabled", "getObserversCount", "getCurrentResult", "isStaleByTime", "_this$retryer2", "find", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "_this$retryer3", "shouldFetchOnReconnect", "addObserver", "notify", "removeObserver", "invalidate", "fetch", "fetchOptions", "_this$options$behavio", "_context$fetchOptions", "_this$retryer4", "abortController", "AbortController", "getAbortController", "queryFnContext", "pageParam", "addSignalProperty", "object", "defineProperty", "enumerable", "get", "signal", "context", "fetchFn", "_context$fetchOptions2", "behavior", "onFetch", "revertState", "_this$cache$config$on", "_this$cache$config", "_this$cache$config$on2", "_this$cache$config2", "onSettled", "isFetchingOptimistic", "_this$cache$config$on3", "_this$cache$config3", "_this$cache$config$on4", "_this$cache$config4", "Error", "action", "_action$meta", "_action$dataUpdatedAt", "reducer", "onQueryUpdate", "Query<PERSON>ache", "queries", "queriesMap", "build", "client", "_options$queryHash", "<PERSON><PERSON><PERSON><PERSON>", "defaultQueryOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queryInMap", "clear", "getAll", "findAll", "Mutation", "mutationId", "mutationCache", "_this$retryer$continu", "execute", "async", "executeMutation", "_this$options$retry", "mutationFn", "variables", "restored", "_this$mutationCache$c3", "_this$mutationCache$c4", "_this$options$onSucce", "_this$options2", "_this$mutationCache$c5", "_this$mutationCache$c6", "_this$options$onSettl", "_this$options3", "_this$mutationCache$c", "_this$mutationCache$c2", "_this$options$onMutat", "_this$options", "onMutate", "_this$mutationCache$c7", "_this$mutationCache$c8", "_this$options$onError", "_this$options4", "_this$mutationCache$c9", "_this$mutationCache$c10", "_this$options$onSettl2", "_this$options5", "failureReason", "isPaused", "onMutationUpdate", "MutationCache", "mutations", "defaultMutationOptions", "getMutationDefaults", "resumePausedMutations", "_this$resuming", "resuming", "pausedMutations", "infiniteQueryBehavior", "_context$fetchOptions3", "_context$fetchOptions4", "_context$state$data", "_context$state$data2", "refetchPage", "fetchMore", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "pages", "oldPageParams", "pageParams", "newPageParams", "cancelled", "buildNewPages", "param", "page", "previous", "fetchPage", "_context$signal", "_context$signal2", "aborted", "queryFnResult", "getNextPageParam", "getPreviousPageParam", "hasNextPage", "nextPageParam", "hasPreviousPage", "previousPageParam", "QueryObserver", "trackedProps", "selectError", "bindMethods", "<PERSON><PERSON><PERSON><PERSON>", "shouldFetchOnMount", "executeFetch", "updateTimers", "shouldFetchOn", "refetchOnReconnect", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON>ache", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "createResult", "optimisticResult", "keepPreviousData", "placeholderData", "isPlaceholderData", "shouldAssignObserverCurrentProperties", "currentResult", "currentResultOptions", "currentResultState", "trackResult", "trackedResult", "configurable", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchOptimistic", "defaultedOptions", "_fetchOptions$cancelR", "throwOnError", "staleTimeoutId", "_this$options$refetch", "refetchInterval", "nextInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "clearInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "isPreviousData", "_optimisticResults", "fetchOnMount", "fetchOptionally", "isSuccess", "select", "selectFn", "selectResult", "isFetching", "isLoading", "isError", "isInitialLoading", "isFetched", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isRefetchError", "nextResult", "defaultNotifyOptions", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "useErrorBoundary", "<PERSON><PERSON><PERSON>", "has", "shouldNotifyListeners", "retryOnMount", "shouldLoadOnMount", "refetchOnMount", "field", "suspense", "defaultShouldDehydrateMutation", "defaultShouldDehydrateQuery", "fetchNextPage", "fetchPreviousPage", "_state$fetchMeta", "_state$fetchMeta$fetc", "_state$fetchMeta2", "_state$fetchMeta2$fet", "_state$data", "_state$data2", "mutate", "_this$currentMutation", "getMutationCache", "currentMutation", "_this$currentMutation2", "mutateOptions", "isPending", "isIdle", "_this$mutateOptions$o", "_this$mutateOptions", "_this$mutateOptions$o2", "_this$mutateOptions2", "_this$mutateOptions$o3", "_this$mutateOptions3", "_this$mutateOptions$o4", "_this$mutateOptions4", "observersMap", "setQueries", "onUpdate", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "defaultedQueryOptions", "newObservers", "map", "newObserversMap", "fromEntries", "newResult", "hasIndexChange", "index", "getQueries", "getObservers", "prevObserversMap", "Map", "matchingObservers", "flatMap", "matchedQueryHashes", "unmatchedQueries", "matchingObserversSet", "unmatchedObservers", "prevObserver", "getObserver", "currentObserver", "newOrReusedObservers", "previouslyUsedObserver", "concat", "indexOf", "slice", "replaceAt", "queryCache", "queryDefaults", "mutationDefaults", "mountCount", "mount", "unsubscribeFocus", "unsubscribeOnline", "unmount", "_this$unsubscribeFocu", "_this$unsubscribeOnli", "isMutating", "getQueryData", "_this$queryCache$find", "ensureQueryData", "parsedOptions", "cachedData", "<PERSON><PERSON><PERSON><PERSON>", "getQueriesData", "query<PERSON>eyOrFilters", "setQueryData", "updater", "input", "functionalUpdate", "setQueriesData", "getQueryState", "_this$queryCache$find2", "removeQueries", "resetQueries", "refetchFilters", "refetchQueries", "cancelQueries", "promises", "all", "invalidateQueries", "_ref", "_filters$refetchType", "refetchType", "_options$cancelRefetc", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "prefetchInfiniteQuery", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstMatchingDefaults", "setMutationDefaults", "_defaulted", "dehydrateMutations", "shouldDehydrateMutation", "dehydrateMutation", "dehydrateQueries", "shouldDehydrateQuery", "dehydrate<PERSON><PERSON>y", "dehydratedState", "dehydratedMutation", "_options$defaultOptio", "_options$defaultOptio2", "_ignored", "dehydratedQueryState"], "mappings": "iPAEO,MAAMA,EAGXC,cACEC,KAAKC,UAAY,IAAIC,IACrBF,KAAKG,UAAYH,KAAKG,UAAUC,KAAKJ,MAGvCG,UAAUE,GACR,MAAMC,EAAW,CAAED,YAKnB,OAJAL,KAAKC,UAAUM,IAAID,GAEnBN,KAAKQ,cAEE,KACLR,KAAKC,UAAUQ,OAAOH,GACtBN,KAAKU,iBAITC,eACE,OAAOX,KAAKC,UAAUW,KAAO,EAGrBJ,eAIAE,kBCwCCG,MAAAA,EAA6B,oBAAXC,QAA0B,SAAUA,OAE5D,SAASC,KAaT,SAASC,EAAeC,GAC7B,MAAwB,iBAAVA,GAAsBA,GAAS,GAAKA,IAAUC,IAGvD,SAASC,EAAcC,EAAaC,GACzC,OAAOD,EAAOE,QAAQC,IAAOF,EAAOG,SAASD,KASxC,SAASE,EAAeC,EAAmBC,GAChD,OAAOC,KAAKC,IAAIH,GAAaC,GAAa,GAAKG,KAAKC,MAAO,GAGtD,SAASC,EAIdC,EACAC,EACAC,GAEA,OAAKC,EAAWH,GAII,mBAATC,EACF,IAAKC,EAAME,SAAUJ,EAAMK,QAASJ,GAGtC,IAAKA,EAAMG,SAAUJ,GAPnBA,EA+BJ,SAASM,EAIdN,EACAC,EACAC,GAEA,OACEC,EAAWH,GAAQ,CAAC,IAAKC,EAAMG,SAAUJ,GAAQE,GAAQ,CAACF,GAAQ,GAAIC,GAmBnE,SAASM,EACdC,EACAC,GAEA,MAAMC,KACJA,EAAO,MADHC,MAEJA,EAFIC,YAGJA,EAHIC,UAIJA,EAJIT,SAKJA,EALIU,MAMJA,GACEN,EAEJ,GAAIL,EAAWC,GACb,GAAIO,GACF,GAAIF,EAAMM,YAAcC,EAAsBZ,EAAUK,EAAMQ,SAC5D,OAAO,OAEJ,IAAKC,EAAgBT,EAAML,SAAUA,GAC1C,OAAO,EAIX,GAAa,QAATM,EAAgB,CAClB,MAAMS,EAAWV,EAAMU,WACvB,GAAa,WAATT,IAAsBS,EACxB,OAAO,EAET,GAAa,aAATT,GAAuBS,EACzB,OAAO,EAIX,OAAqB,kBAAVL,GAAuBL,EAAMW,YAAcN,WAK7B,IAAhBF,GACPA,IAAgBH,EAAMY,MAAMT,gBAK1BC,IAAcA,EAAUJ,KAOvB,SAASa,EACdd,EACAe,GAEA,MAAMZ,MAAEA,EAAFa,SAASA,EAATX,UAAmBA,EAAnBY,YAA8BA,GAAgBjB,EACpD,GAAIL,EAAWsB,GAAc,CAC3B,IAAKF,EAASN,QAAQQ,YACpB,OAAO,EAET,GAAId,GACF,GACEe,EAAaH,EAASN,QAAQQ,eAAiBC,EAAaD,GAE5D,OAAO,OAEJ,IAAKP,EAAgBK,EAASN,QAAQQ,YAAaA,GACxD,OAAO,EAIX,OACsB,kBAAbD,GACoB,YAA1BD,EAASF,MAAMM,SAA0BH,MAKxCX,IAAcA,EAAUU,IAOvB,SAASP,EACdZ,EACAa,GAGA,QADsB,MAAPA,SAAAA,EAASW,iBAAkBF,GAC5BtB,GAOT,SAASsB,EAAatB,GAC3B,OAAOyB,KAAKC,UAAU1B,GAAU,CAAC2B,EAAGC,IAClCC,EAAcD,GACVE,OAAOC,KAAKH,GACTI,OACAC,QAAO,CAACC,EAAQC,KACfD,EAAOC,GAAOP,EAAIO,GACXD,IACN,IACLN,IAOD,SAASd,EAAgBsB,EAAaC,GAC3C,OAAOC,EAAiBF,EAAGC,GAMtB,SAASC,EAAiBF,EAAQC,GACvC,OAAID,IAAMC,UAICD,UAAaC,OAIpBD,IAAKC,GAAkB,iBAAND,GAA+B,iBAANC,KACpCP,OAAOC,KAAKM,GAAGE,MAAMJ,IAASG,EAAiBF,EAAED,GAAME,EAAEF,OAY9D,SAASK,EAAiBJ,EAAQC,GACvC,GAAID,IAAMC,EACR,OAAOD,EAGT,MAAMK,EAAQC,EAAaN,IAAMM,EAAaL,GAE9C,GAAII,GAAUZ,EAAcO,IAAMP,EAAcQ,GAAK,CACnD,MAAMM,EAAQF,EAAQL,EAAEQ,OAASd,OAAOC,KAAKK,GAAGQ,OAC1CC,EAASJ,EAAQJ,EAAIP,OAAOC,KAAKM,GACjCS,EAAQD,EAAOD,OACfG,EAAYN,EAAQ,GAAK,GAE/B,IAAIO,EAAa,EAEjB,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAOG,IAAK,CAC9B,MAAMd,EAAMM,EAAQQ,EAAIJ,EAAOI,GAC/BF,EAAKZ,GAAOK,EAAiBJ,EAAED,GAAME,EAAEF,IACnCY,EAAKZ,KAASC,EAAED,IAClBa,IAIJ,OAAOL,IAAUG,GAASE,IAAeL,EAAQP,EAAIW,EAGvD,OAAOV,EAMF,SAASa,EAAuBd,EAAMC,GAC3C,GAAKD,IAAMC,GAAOA,IAAMD,EACtB,OAAO,EAGT,IAAK,MAAMD,KAAOC,EAChB,GAAIA,EAAED,KAASE,EAAEF,GACf,OAAO,EAIX,OAAO,EAGF,SAASO,EAAa9D,GAC3B,OAAOuE,MAAMC,QAAQxE,IAAUA,EAAMgE,SAAWd,OAAOC,KAAKnD,GAAOgE,OAI9D,SAASf,EAAcwB,GAC5B,IAAKC,EAAmBD,GACtB,OAAO,EAIT,MAAME,EAAOF,EAAE3F,YACf,QAAoB,IAAT6F,EACT,OAAO,EAIT,MAAMC,EAAOD,EAAKE,UAClB,QAAKH,EAAmBE,MAKnBA,EAAKE,eAAe,iBAQ3B,SAASJ,EAAmBD,GAC1B,MAA6C,oBAAtCvB,OAAO2B,UAAUE,SAASC,KAAKP,GAGjC,SAAStD,EAAWnB,GACzB,OAAOuE,MAAMC,QAAQxE,GAOhB,SAASiF,EAAMC,GACpB,OAAO,IAAIC,SAASC,IAClBC,WAAWD,EAASF,MAQjB,SAASI,EAAkBC,GAChCN,EAAM,GAAGO,KAAKD,GAUT,SAASE,EAGdC,EAA6BC,EAAa1D,GAE1C,OAAA,MAAIA,EAAQ2D,aAAR3D,EAAQ2D,YAAcF,EAAUC,GAC3BD,EACuC,mBAA9BzD,EAAQ4D,kBACjB5D,EAAQ4D,kBAAkBH,EAAUC,IACJ,IAA9B1D,EAAQ4D,kBAEVjC,EAAiB8B,EAAUC,GAE7BA,EC3VIG,MAAAA,EAAe,IAlFrB,cAA2BjH,EAMhCC,cACEiH,QACAhH,KAAKiH,MAASC,IAGZ,IAAKrG,GAAYC,OAAOqG,iBAAkB,CACxC,MAAM9G,EAAW,IAAM6G,IAKvB,OAHApG,OAAOqG,iBAAiB,mBAAoB9G,GAAU,GACtDS,OAAOqG,iBAAiB,QAAS9G,GAAU,GAEpC,KAELS,OAAOsG,oBAAoB,mBAAoB/G,GAC/CS,OAAOsG,oBAAoB,QAAS/G,MAOlCG,cACHR,KAAKqH,SACRrH,KAAKsH,iBAAiBtH,KAAKiH,OAIrBvG,gBACkB,IAAA6G,EAArBvH,KAAKW,iBACR,OAAA4G,EAAAvH,KAAKqH,UAALE,EAAAtB,KAAAjG,MACAA,KAAKqH,aAAUG,GAInBF,iBAAiBL,GAAsB,IAAAQ,EACrCzH,KAAKiH,MAAQA,EACb,OAAAQ,EAAAzH,KAAKqH,UAALI,EAAAxB,KAAAjG,MACAA,KAAKqH,QAAUJ,GAAOS,IACG,kBAAZA,EACT1H,KAAK2H,WAAWD,GAEhB1H,KAAKkH,aAKXS,WAAWD,GACO1H,KAAK0H,UAAYA,IAE/B1H,KAAK0H,QAAUA,EACf1H,KAAKkH,WAITA,UACElH,KAAKC,UAAU2H,SAAQ,EAAGvH,eACxBA,OAIJwH,YACE,MAA4B,kBAAjB7H,KAAK0H,QACP1H,KAAK0H,QAIU,oBAAbI,UAIJ,MAACN,EAAW,UAAW,aAAahG,SACzCsG,SAASC,mBC7ETC,EAAe,CAAC,SAAU,WAwFnBC,MAAAA,EAAgB,IAtFtB,cAA4BnI,EAMjCC,cACEiH,QACAhH,KAAKiH,MAASiB,IAGZ,IAAKrH,GAAYC,OAAOqG,iBAAkB,CACxC,MAAM9G,EAAW,IAAM6H,IAMvB,OAJAF,EAAaJ,SAASO,IACpBrH,OAAOqG,iBAAiBgB,EAAO9H,GAAU,MAGpC,KAEL2H,EAAaJ,SAASO,IACpBrH,OAAOsG,oBAAoBe,EAAO9H,SASlCG,cACHR,KAAKqH,SACRrH,KAAKsH,iBAAiBtH,KAAKiH,OAIrBvG,gBACkB,IAAA6G,EAArBvH,KAAKW,iBACR,OAAA4G,EAAAvH,KAAKqH,UAALE,EAAAtB,KAAAjG,MACAA,KAAKqH,aAAUG,GAInBF,iBAAiBL,GAAsB,IAAAQ,EACrCzH,KAAKiH,MAAQA,EACb,OAAAQ,EAAAzH,KAAKqH,UAALI,EAAAxB,KAAAjG,MACAA,KAAKqH,QAAUJ,GAAOmB,IACE,kBAAXA,EACTpI,KAAKqI,UAAUD,GAEfpI,KAAKkI,cAKXG,UAAUD,GACQpI,KAAKoI,SAAWA,IAG9BpI,KAAKoI,OAASA,EACdpI,KAAKkI,YAITA,WACElI,KAAKC,UAAU2H,SAAQ,EAAGvH,eACxBA,OAIJiI,WACE,MAA2B,kBAAhBtI,KAAKoI,OACPpI,KAAKoI,OAIS,oBAAdG,gBACqB,IAArBA,UAAUC,QAKZD,UAAUC,SCjDrB,SAASC,EAAkBC,GACzB,OAAO9G,KAAK+G,IAAI,IAAO,GAAKD,EAAc,KAGrC,SAASE,EAASC,GACvB,MAAqC,YAA7BA,MAAAA,EAAAA,EAAe,WACnBZ,EAAcK,WAIb,MAAMQ,EAGX/I,YAAYmD,GACVlD,KAAK+I,OAAS7F,MAAAA,OAAAA,EAAAA,EAAS6F,OACvB/I,KAAKgJ,OAAS9F,MAAAA,OAAAA,EAAAA,EAAS8F,QAIpB,SAASC,EAAiBhI,GAC/B,OAAOA,aAAiB6H,EAGnB,SAASI,EACdC,GAEA,IAGIC,EACAC,EACAC,EALAC,GAAmB,EACnBb,EAAe,EACfc,GAAa,EAKjB,MAAMC,EAAU,IAAIrD,SAAe,CAACsD,EAAcC,KAChDN,EAAiBK,EACjBJ,EAAgBK,KAkBZC,EAAc,KACjB7C,EAAac,aACU,WAAvBsB,EAAON,cAA6BZ,EAAcK,WAE/CjC,EAAWpF,IACVuI,IACHA,GAAa,EACb,MAAAL,EAAOU,WAAPV,EAAOU,UAAY5I,GACT,MAAVmI,GAAAA,IACAC,EAAepI,KAIb6I,EAAU7I,IACTuI,IACHA,GAAa,EACb,MAAAL,EAAOY,SAAPZ,EAAOY,QAAU9I,GACP,MAAVmI,GAAAA,IACAE,EAAcrI,KAIZ+I,EAAQ,IACL,IAAI5D,SAAS6D,IAClBb,EAAcnI,IACZ,MAAMiJ,EAAcV,IAAeI,IAInC,OAHIM,GACFD,EAAgBhJ,GAEXiJ,GAETf,MAAAA,EAAOgB,SAAPhB,EAAOgB,aACN1D,MAAK,KACN2C,OAAa5B,EACRgC,GACHL,MAAAA,EAAOiB,YAAPjB,EAAOiB,gBAMPC,EAAM,KAEV,GAAIb,EACF,OAGF,IAAIc,EAGJ,IACEA,EAAiBnB,EAAOoB,KACxB,MAAOC,GACPF,EAAiBlE,QAAQ0D,OAAOU,GAGlCpE,QAAQC,QAAQiE,GACb7D,KAAKJ,GACLoE,OAAOD,IAAU,IAAAE,EAAAC,EAEhB,GAAInB,EACF,OAIF,MAAMoB,SAAQzB,EAAAA,EAAOyB,SAAS,EACxBC,SAAa1B,EAAAA,EAAO0B,cAAcpC,EAClCqC,EACkB,mBAAfD,EACHA,EAAWnC,EAAc8B,GACzBK,EACAE,GACM,IAAVH,GACkB,iBAAVA,GAAsBlC,EAAekC,GAC3B,mBAAVA,GAAwBA,EAAMlC,EAAc8B,IAElDjB,GAAqBwB,GAMzBrC,IAGA,MAAAS,EAAO6B,QAAP7B,EAAO6B,OAAStC,EAAc8B,GAG9BtE,EAAM4E,GAEHrE,MAAK,KACJ,GAAImD,IACF,OAAOI,OAIVvD,MAAK,KACA8C,EACFO,EAAOU,GAEPH,QAtBJP,EAAOU,OAmCf,OANI5B,EAASO,EAAON,aAClBwB,IAEAL,IAAQvD,KAAK4D,GAGR,CACLZ,UACAwB,OAlIcC,IACT1B,IACHM,EAAO,IAAIhB,EAAeoC,IAE1B/B,MAAAA,EAAOgC,OAAPhC,EAAOgC,UA+HTC,SAAU,KACS,MAAGhC,OAAH,EAAGA,KACCK,EAAUrD,QAAQC,UAEzCgF,YAhIkB,KAClB9B,GAAmB,GAgInB+B,cA7HoB,KACpB/B,GAAmB,ICpFhB,MAAMgC,EAAwBC,QC2FxBC,MAAAA,EAvFN,WACL,IAAIC,EAA0B,GAC1BC,EAAe,EACfC,EAA4BpF,IAC9BA,KAEEqF,EAAsCrF,IACxCA,KAGF,MAcMsF,EAAYtF,IACZmF,EACFD,EAAMK,KAAKvF,GAEXD,GAAkB,KAChBqF,EAASpF,OAkBTwF,EAAQ,KACZ,MAAMC,EAAgBP,EACtBA,EAAQ,GACJO,EAAchH,QAChBsB,GAAkB,KAChBsF,GAAc,KACZI,EAAcrE,SAASpB,IACrBoF,EAASpF,aAuBnB,MAAO,CACL0F,MApEgB1F,IAChB,IAAIjC,EACJoH,IACA,IACEpH,EAASiC,IACD,QACRmF,IACKA,GACHK,IAGJ,OAAOzH,GA0DP4H,WAzCA3F,GAEO,IAAI4F,KACTN,GAAS,KACPtF,KAAY4F,OAsChBN,WACAO,kBAhByB9B,IACzBqB,EAAWrB,GAgBX+B,uBAT8B/B,IAC9BsB,EAAgBtB,IAaSgC,GCjGtB,MAAeC,EAIpBC,UACEzM,KAAK0M,iBAGGC,aACR3M,KAAK0M,iBAED1L,EAAehB,KAAK4M,aACtB5M,KAAK6M,UAAYvG,YAAW,KAC1BtG,KAAK8M,mBACJ9M,KAAK4M,YAIFG,gBAAgBC,GAExBhN,KAAK4M,UAAYhL,KAAKC,IACpB7B,KAAK4M,WAAa,EAClBI,MAAAA,EAAAA,EAAiBnM,EAAWK,IAAW,KAIjCwL,iBACJ1M,KAAK6M,YACPI,aAAajN,KAAK6M,WAClB7M,KAAK6M,eAAYrF,IC6GhB,MAAM0F,UAKHV,EAiBRzM,YAAYoJ,GACVnC,QAEAhH,KAAKmN,qBAAsB,EAC3BnN,KAAKoN,eAAiBjE,EAAOiE,eAC7BpN,KAAKqN,WAAWlE,EAAOjG,SACvBlD,KAAKsN,UAAY,GACjBtN,KAAKuN,MAAQpE,EAAOoE,MACpBvN,KAAKwN,OAASrE,EAAOqE,QAAUjC,EAC/BvL,KAAKqC,SAAW8G,EAAO9G,SACvBrC,KAAKgD,UAAYmG,EAAOnG,UACxBhD,KAAKyN,aAAetE,EAAO7F,OA0a/B,SAMEJ,GAEA,MAAM0D,EAC2B,mBAAxB1D,EAAQwK,YACVxK,EAAQwK,cACTxK,EAAQwK,YAERC,OAA0B,IAAT/G,EAEjBgH,EAAuBD,EACe,mBAAjCzK,EAAQ0K,qBACZ1K,EAAQ0K,uBACT1K,EAAQ0K,qBACV,EAEJ,MAAO,CACLhH,OACAiH,gBAAiB,EACjBC,cAAeH,EAAUC,MAAAA,EAAAA,EAAwB9L,KAAKC,MAAQ,EAC9DyI,MAAO,KACPuD,iBAAkB,EAClBC,eAAgB,EAChBC,kBAAmB,EACnBC,mBAAoB,KACpBC,UAAW,KACXC,eAAe,EACfxK,OAAQ+J,EAAU,UAAY,UAC9B9K,YAAa,QA3cuBwL,CAAgBrO,KAAKkD,SACzDlD,KAAKsD,MAAQtD,KAAKyN,aAClBzN,KAAK2M,aAGH2B,WACF,OAAOtO,KAAKkD,QAAQoL,KAGdjB,WACNnK,GAEAlD,KAAKkD,QAAU,IAAKlD,KAAKoN,kBAAmBlK,GAE5ClD,KAAK+M,gBAAgB/M,KAAKkD,QAAQ0J,WAG1BE,iBACH9M,KAAKsN,UAAUrI,QAAqC,SAA3BjF,KAAKsD,MAAMT,aACvC7C,KAAKuN,MAAMgB,OAAOvO,MAItBwO,QACEC,EACAvL,GAEA,MAAM0D,EAAOF,EAAY1G,KAAKsD,MAAMsD,KAAM6H,EAASzO,KAAKkD,SAUxD,OAPAlD,KAAK0O,SAAS,CACZ9H,OACAjE,KAAM,UACNmL,cAAe5K,MAAAA,OAAAA,EAAAA,EAASxB,UACxBiN,OAAQzL,MAAAA,OAAAA,EAAAA,EAASyL,SAGZ/H,EAGTgI,SACEtL,EACAuL,GAEA7O,KAAK0O,SAAS,CAAE/L,KAAM,WAAYW,QAAOuL,oBAG3C5D,OAAO/H,GAAwC,IAAA4L,EAC7C,MAAMrF,EAAUzJ,KAAKyJ,QAErB,OADA,OAAAqF,EAAA9O,KAAK+O,UAALD,EAAc7D,OAAO/H,GACduG,EAAUA,EAAQhD,KAAK1F,GAAM0J,MAAM1J,GAAQqF,QAAQC,UAG5DoG,UACEzF,MAAMyF,UAENzM,KAAKiL,OAAO,CAAEjC,QAAQ,IAGxBgG,QACEhP,KAAKyM,UACLzM,KAAK4O,SAAS5O,KAAKyN,cAGrBrK,WACE,OAAOpD,KAAKsN,UAAU1I,MAAMqK,IAA0C,IAA7BA,EAAS/L,QAAQgM,UAG5DC,aACE,OAAOnP,KAAKoP,oBAAsB,IAAMpP,KAAKoD,WAG/CC,UACE,OACErD,KAAKsD,MAAM8K,gBACVpO,KAAKsD,MAAMwK,eACZ9N,KAAKsN,UAAU1I,MAAMqK,GAAaA,EAASI,mBAAmBhM,UAIlEiM,cAAc3N,EAAY,GACxB,OACE3B,KAAKsD,MAAM8K,gBACVpO,KAAKsD,MAAMwK,gBACXrM,EAAezB,KAAKsD,MAAMwK,cAAenM,GAI9CuF,UAAgB,IAAAqI,EACd,MAAMN,EAAWjP,KAAKsN,UAAUkC,MAAMjO,GAAMA,EAAEkO,6BAE1CR,GACFA,EAASS,QAAQ,CAAEC,eAAe,WAI/BZ,EAAAA,KAAAA,YAAS3D,WAGhBlD,WAAiB,IAAA0H,EACf,MAAMX,EAAWjP,KAAKsN,UAAUkC,MAAMjO,GAAMA,EAAEsO,2BAE1CZ,GACFA,EAASS,QAAQ,CAAEC,eAAe,WAI/BZ,EAAAA,KAAAA,YAAS3D,WAGhB0E,YAAYb,GACLjP,KAAKsN,UAAU9L,SAASyN,KAC3BjP,KAAKsN,UAAUvB,KAAKkD,GAGpBjP,KAAK0M,iBAEL1M,KAAKuN,MAAMwC,OAAO,CAAEpN,KAAM,gBAAiBD,MAAO1C,KAAMiP,cAI5De,eAAef,GACTjP,KAAKsN,UAAU9L,SAASyN,KAC1BjP,KAAKsN,UAAYtN,KAAKsN,UAAUhM,QAAQC,GAAMA,IAAM0N,IAE/CjP,KAAKsN,UAAUrI,SAGdjF,KAAK+O,UACH/O,KAAKmN,oBACPnN,KAAK+O,QAAQ9D,OAAO,CAAElC,QAAQ,IAE9B/I,KAAK+O,QAAQ1D,eAIjBrL,KAAK2M,cAGP3M,KAAKuN,MAAMwC,OAAO,CAAEpN,KAAM,kBAAmBD,MAAO1C,KAAMiP,cAI9DG,oBACE,OAAOpP,KAAKsN,UAAUrI,OAGxBgL,aACOjQ,KAAKsD,MAAM8K,eACdpO,KAAK0O,SAAS,CAAE/L,KAAM,eAI1BuN,MACEhN,EACAiN,GACgB,IAAAC,EAAAC,EAChB,GAA+B,SAA3BrQ,KAAKsD,MAAMT,YACb,GAAI7C,KAAKsD,MAAMwK,eAAiBqC,MAAAA,GAAAA,EAAcR,cAE5C3P,KAAKiL,OAAO,CAAEjC,QAAQ,SACjB,GAAIhJ,KAAKyJ,QAAS,CAAA,IAAA6G,EAIvB,OAFA,OAAAA,EAAAtQ,KAAK+O,UAALuB,EAAchF,gBAEPtL,KAAKyJ,QAWhB,GANIvG,GACFlD,KAAKqN,WAAWnK,IAKblD,KAAKkD,QAAQZ,QAAS,CACzB,MAAM2M,EAAWjP,KAAKsN,UAAUkC,MAAMjO,GAAMA,EAAE2B,QAAQZ,UAClD2M,GACFjP,KAAKqN,WAAW4B,EAAS/L,SAY7B,MAAMqN,EPoDH,WACL,GAA+B,mBAApBC,gBACT,OAAO,IAAIA,gBOtDaC,GAGlBC,EAAkD,CACtDrO,SAAUrC,KAAKqC,SACfsO,eAAWnJ,EACX8G,KAAMtO,KAAKsO,MAMPsC,EAAqBC,IACzB1M,OAAO2M,eAAeD,EAAQ,SAAU,CACtCE,YAAY,EACZC,IAAK,KACH,GAAIT,EAEF,OADAvQ,KAAKmN,qBAAsB,EACpBoD,EAAgBU,WAO/BL,EAAkBF,GAGlB,MAWMQ,EAAgE,CACpEf,eACAjN,QAASlD,KAAKkD,QACdb,SAAUrC,KAAKqC,SACfiB,MAAOtD,KAAKsD,MACZ6N,QAhBc,IACTnR,KAAKkD,QAAQZ,SAKlBtC,KAAKmN,qBAAsB,EACpBnN,KAAKkD,QAAQZ,QAAQoO,IALnBtK,QAAQ0D,OAAR,iCAC4B9J,KAAKkD,QAAQF,UADhD,MA4BF,IAAAoO,GAXFR,EAAkBM,GAElB,OAAKhO,EAAAA,KAAAA,QAAQmO,WAAbjB,EAAuBkB,QAAQJ,GAG/BlR,KAAKuR,YAAcvR,KAAKsD,MAIK,SAA3BtD,KAAKsD,MAAMT,aACX7C,KAAKsD,MAAM6K,oBAAXkC,EAAyBa,EAAQf,qBAARE,EAAsB/B,QAE/CtO,KAAK0O,SAAS,CAAE/L,KAAM,QAAS2L,YAAM4C,EAAAA,EAAQf,qBAARiB,EAAsB9C,OAG7D,MAAMvE,EAAWS,IASe,IAAAgH,EAAAC,EAAAC,EAAAC,GAPxB1I,EAAiBuB,IAAUA,EAAMxB,QACrChJ,KAAK0O,SAAS,CACZ/L,KAAM,QACN6H,MAAOA,IAINvB,EAAiBuB,MAEQA,OAAvB+C,GAAAA,EAAAA,KAAAA,MAAMpE,QAAOY,UAAUS,EAAAA,KAAAA,EAAAA,EAAOxK,MACnC,OAAA0R,GAAAC,EAAA3R,KAAKuN,MAAMpE,QAAOyI,YAAlBF,EAAAzL,KAAA0L,EACE3R,KAAKsD,MAAMsD,KACX4D,EACAxK,OAQCA,KAAK6R,sBAER7R,KAAK2M,aAEP3M,KAAK6R,sBAAuB,GAmD9B,OA/CA7R,KAAK+O,QAAU7F,EAAc,CAC3BqB,GAAI2G,EAAQC,QACZhG,MAAK,MAAEoF,OAAF,EAAEA,EAAiBpF,MAAM/K,KAAKmQ,GACnC1G,UAAYjD,IAAS,IAAAkL,EAAAC,EAAAC,EAAAC,OACC,IAATrL,GAUX5G,KAAKwO,QAAQ5H,GAGiBA,OAAzB2G,GAAAA,EAAAA,KAAAA,MAAMpE,QAAOU,YAAYjD,EAAAA,KAAAA,EAAAA,EAAM5G,MACpC,OAAAgS,GAAAC,EAAAjS,KAAKuN,MAAMpE,QAAOyI,YAAlBI,EAAA/L,KAAAgM,EACErL,EACA5G,KAAKsD,MAAMkH,MACXxK,MAGGA,KAAK6R,sBAER7R,KAAK2M,aAEP3M,KAAK6R,sBAAuB,GAlB1B9H,EAAQ,IAAImI,MAASlS,KAAKgD,kCAoB9B+G,UACAiB,OAAQ,CAACtC,EAAc8B,KACrBxK,KAAK0O,SAAS,CAAE/L,KAAM,SAAU+F,eAAc8B,WAEhDL,QAAS,KACPnK,KAAK0O,SAAS,CAAE/L,KAAM,WAExByH,WAAY,KACVpK,KAAK0O,SAAS,CAAE/L,KAAM,cAExBiI,MAAOsG,EAAQhO,QAAQ0H,MACvBC,WAAYqG,EAAQhO,QAAQ2H,WAC5BhC,YAAaqI,EAAQhO,QAAQ2F,cAG/B7I,KAAKyJ,QAAUzJ,KAAK+O,QAAQtF,QAErBzJ,KAAKyJ,QAGNiF,SAASyD,GAgFfnS,KAAKsD,MA9EHA,KAC8B,IAAA8O,EAAAC,EAC9B,OAAQF,EAAOxP,MACb,IAAK,SACH,MAAO,IACFW,EACH2K,kBAAmBkE,EAAOzJ,aAC1BwF,mBAAoBiE,EAAO3H,OAE/B,IAAK,QACH,MAAO,IACFlH,EACHT,YAAa,UAEjB,IAAK,WACH,MAAO,IACFS,EACHT,YAAa,YAEjB,IAAK,QACH,MAAO,IACFS,EACH2K,kBAAmB,EACnBC,mBAAoB,KACpBC,iBAAWgE,EAAAA,EAAO7D,QAAQ,KAC1BzL,YAAa+F,EAAS5I,KAAKkD,QAAQ2F,aAC/B,WACA,aACCvF,EAAMwK,eAAiB,CAC1BtD,MAAO,KACP5G,OAAQ,YAGd,IAAK,UACH,MAAO,IACFN,EACHsD,KAAMuL,EAAOvL,KACbiH,gBAAiBvK,EAAMuK,gBAAkB,EACzCC,qBAAauE,EAAEF,EAAOrE,iBAAiBhM,KAAKC,MAC5CyI,MAAO,KACP4D,eAAe,EACfxK,OAAQ,cACHuO,EAAOxD,QAAU,CACpB9L,YAAa,OACboL,kBAAmB,EACnBC,mBAAoB,OAG1B,IAAK,QACH,MAAM1D,EAAQ2H,EAAO3H,MAErB,OAAIvB,EAAiBuB,IAAUA,EAAMzB,QAAU/I,KAAKuR,YAC3C,IAAKvR,KAAKuR,YAAa1O,YAAa,QAGtC,IACFS,EACHkH,MAAOA,EACPuD,iBAAkBzK,EAAMyK,iBAAmB,EAC3CC,eAAgBlM,KAAKC,MACrBkM,kBAAmB3K,EAAM2K,kBAAoB,EAC7CC,mBAAoB1D,EACpB3H,YAAa,OACbe,OAAQ,SAEZ,IAAK,aACH,MAAO,IACFN,EACH8K,eAAe,GAEnB,IAAK,WACH,MAAO,IACF9K,KACA6O,EAAO7O,SAKLgP,CAAQtS,KAAKsD,OAE1BmI,EAAcS,OAAM,KAClBlM,KAAKsN,UAAU1F,SAASqH,IACtBA,EAASsD,cAAcJ,MAGzBnS,KAAKuN,MAAMwC,OAAO,CAAErN,MAAO1C,KAAM2C,KAAM,UAAWwP,eCpgBjD,MAAMK,UAAmB1S,EAM9BC,YAAYoJ,GACVnC,QACAhH,KAAKmJ,OAASA,GAAU,GACxBnJ,KAAKyS,QAAU,GACfzS,KAAK0S,WAAa,GAGpBC,MACEC,EACA1P,EACAI,GAC+C,IAAAuP,EAC/C,MAAMxQ,EAAWa,EAAQb,SACnBW,EACiBC,OAArBC,EAAAA,EAAQF,WAAaC,EAAAA,EAAsBZ,EAAUa,GACvD,IAAIR,EAAQ1C,KAAKgR,IAA4ChO,GAe7D,OAbKN,IACHA,EAAQ,IAAIwK,EAAM,CAChBK,MAAOvN,KACPwN,OAAQoF,EAAOE,YACfzQ,WACAW,YACAE,QAAS0P,EAAOG,oBAAoB7P,GACpCI,QACA8J,eAAgBwF,EAAOI,iBAAiB3Q,KAE1CrC,KAAKO,IAAImC,IAGJA,EAGTnC,IAAImC,GACG1C,KAAK0S,WAAWhQ,EAAMM,aACzBhD,KAAK0S,WAAWhQ,EAAMM,WAAaN,EACnC1C,KAAKyS,QAAQ1G,KAAKrJ,GAClB1C,KAAK+P,OAAO,CACVpN,KAAM,QACND,WAKN6L,OAAO7L,GACL,MAAMuQ,EAAajT,KAAK0S,WAAWhQ,EAAMM,WAErCiQ,IACFvQ,EAAM+J,UAENzM,KAAKyS,QAAUzS,KAAKyS,QAAQnR,QAAQC,GAAMA,IAAMmB,IAE5CuQ,IAAevQ,UACV1C,KAAK0S,WAAWhQ,EAAMM,WAG/BhD,KAAK+P,OAAO,CAAEpN,KAAM,UAAWD,WAInCwQ,QACEzH,EAAcS,OAAM,KAClBlM,KAAKyS,QAAQ7K,SAASlF,IACpB1C,KAAKuO,OAAO7L,SAKlBsO,IAMEhO,GAEA,OAAOhD,KAAK0S,WAAW1P,GAGzBmQ,SACE,OAAOnT,KAAKyS,QAgBdjD,KACEvN,EACAC,GAEA,MAAOO,GAAWF,EAAgBN,EAAMC,GAMxC,YAJ6B,IAAlBO,EAAQG,QACjBH,EAAQG,OAAQ,GAGX5C,KAAKyS,QAAQjD,MAAM9M,GAAUF,EAAWC,EAASC,KAqB1D0Q,QACEnR,EACAC,GAEA,MAAOO,GAAWF,EAAgBN,EAAMC,GACxC,OAAOiC,OAAOC,KAAK3B,GAASwC,OAAS,EACjCjF,KAAKyS,QAAQnR,QAAQoB,GAAUF,EAAWC,EAASC,KACnD1C,KAAKyS,QAGX1C,OAAO5H,GACLsD,EAAcS,OAAM,KAClBlM,KAAKC,UAAU2H,SAAQ,EAAGvH,eACxBA,EAAS8H,SAKfjB,UACEuE,EAAcS,OAAM,KAClBlM,KAAKyS,QAAQ7K,SAASlF,IACpBA,EAAMwE,gBAKZgB,WACEuD,EAAcS,OAAM,KAClBlM,KAAKyS,QAAQ7K,SAASlF,IACpBA,EAAMwF,kBC5JP,MAAMmL,UAKH7G,EAWRzM,YAAYoJ,GACVnC,QAEAhH,KAAKoN,eAAiBjE,EAAOiE,eAC7BpN,KAAKsT,WAAanK,EAAOmK,WACzBtT,KAAKuT,cAAgBpK,EAAOoK,cAC5BvT,KAAKwN,OAASrE,EAAOqE,QAAUjC,EAC/BvL,KAAKsN,UAAY,GACjBtN,KAAKsD,MAAQ6F,EAAO7F,OAAS+K,IAE7BrO,KAAKqN,WAAWlE,EAAOjG,SACvBlD,KAAK2M,aAGPU,WACEnK,GAEAlD,KAAKkD,QAAU,IAAKlD,KAAKoN,kBAAmBlK,GAE5ClD,KAAK+M,gBAAgB/M,KAAKkD,QAAQ0J,WAGhC0B,WACF,OAAOtO,KAAKkD,QAAQoL,KAGtBM,SAAStL,GACPtD,KAAK0O,SAAS,CAAE/L,KAAM,WAAYW,UAGpCwM,YAAYb,GACLjP,KAAKsN,UAAU9L,SAASyN,KAC3BjP,KAAKsN,UAAUvB,KAAKkD,GAGpBjP,KAAK0M,iBAEL1M,KAAKuT,cAAcxD,OAAO,CACxBpN,KAAM,gBACNa,SAAUxD,KACViP,cAKNe,eAAef,GACbjP,KAAKsN,UAAYtN,KAAKsN,UAAUhM,QAAQC,GAAMA,IAAM0N,IAEpDjP,KAAK2M,aAEL3M,KAAKuT,cAAcxD,OAAO,CACxBpN,KAAM,kBACNa,SAAUxD,KACViP,aAIMnC,iBACH9M,KAAKsN,UAAUrI,SACQ,YAAtBjF,KAAKsD,MAAMM,OACb5D,KAAK2M,aAEL3M,KAAKuT,cAAchF,OAAOvO,OAKhCoL,WAA6B,IAAAoI,EAAA1E,EAC3B,OAAA,OAAO0E,EAAA,OAAA1E,EAAA9O,KAAK+O,cAAL,EAAAD,EAAc1D,YAArBoI,EAAmCxT,KAAKyT,UAG7BC,gBACX,MAAMC,EAAkB,KAAM,IAAAC,EAsB5B,OArBA5T,KAAK+O,QAAU7F,EAAc,CAC3BqB,GAAI,IACGvK,KAAKkD,QAAQ2Q,WAGX7T,KAAKkD,QAAQ2Q,WAAW7T,KAAKsD,MAAMwQ,WAFjC1N,QAAQ0D,OAAO,uBAI1BkB,OAAQ,CAACtC,EAAc8B,KACrBxK,KAAK0O,SAAS,CAAE/L,KAAM,SAAU+F,eAAc8B,WAEhDL,QAAS,KACPnK,KAAK0O,SAAS,CAAE/L,KAAM,WAExByH,WAAY,KACVpK,KAAK0O,SAAS,CAAE/L,KAAM,cAExBiI,eAAO5K,KAAKkD,QAAQ0H,SAAS,EAC7BC,WAAY7K,KAAKkD,QAAQ2H,WACzBhC,YAAa7I,KAAKkD,QAAQ2F,cAGrB7I,KAAK+O,QAAQtF,SAGhBsK,EAAiC,YAAtB/T,KAAKsD,MAAMM,OAC5B,IAAI,IAAAoQ,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACF,IAAKR,EAAU,CAAA,IAAAS,EAAAC,EAAAC,EAAAC,EACb3U,KAAK0O,SAAS,CAAE/L,KAAM,UAAWmR,UAAW9T,KAAKkD,QAAQ4Q,kBAEzD,OAAAU,GAAWjB,EAAAA,KAAAA,cAAcpK,QAAOyL,eAAhC,EAAMJ,EACJvO,KAAAwO,EAAAzU,KAAKsD,MAAMwQ,UACX9T,OAEF,MAAMkR,QAAgB,OAAAwD,GAAAC,EAAA3U,KAAKkD,SAAQ0R,eAAb,EAAAF,EAAAzO,KAAA0O,EAAwB3U,KAAKsD,MAAMwQ,YACrD5C,IAAYlR,KAAKsD,MAAM4N,SACzBlR,KAAK0O,SAAS,CACZ/L,KAAM,UACNuO,UACA4C,UAAW9T,KAAKsD,MAAMwQ,YAI5B,MAAMlN,QAAa+M,IAiCnB,aA9BA,OAAMK,GAAAC,EAAAjU,KAAKuT,cAAcpK,QAAOU,gBAAhC,EAAMmK,SACJpN,EACA5G,KAAKsD,MAAMwQ,UACX9T,KAAKsD,MAAM4N,QACXlR,aAGI,OAANkU,UAAWhR,SAAQ2G,gBAAb,EAAAqK,EAAAjO,KAAAkO,EACJvN,EACA5G,KAAKsD,MAAMwQ,UACX9T,KAAKsD,MAAM4N,gBAIb,OAAMkD,GAAAC,EAAArU,KAAKuT,cAAcpK,QAAOyI,gBAAhC,EAAMwC,EACJxN,KAAAA,EAAAA,EACA,KACA5G,KAAKsD,MAAMwQ,UACX9T,KAAKsD,MAAM4N,QACXlR,aAGI,OAANsU,UAAWpR,SAAQ0O,gBAAb,EAAA0C,EAAArO,KAAAsO,EACJ3N,EACA,KACA5G,KAAKsD,MAAMwQ,UACX9T,KAAKsD,MAAM4N,UAGblR,KAAK0O,SAAS,CAAE/L,KAAM,UAAWiE,SAC1BA,EACP,MAAO4D,GACP,IAAI,IAAAqK,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAkCF,YAhCA,OAAMP,GAAAC,EAAA9U,KAAKuT,cAAcpK,QAAOY,cAAhC,EAAM8K,SACJrK,EACAxK,KAAKsD,MAAMwQ,UACX9T,KAAKsD,MAAM4N,QACXlR,aAOI,OAAN+U,UAAW7R,SAAQ6G,cAAb,EAAAgL,EAAA9O,KAAA+O,EACJxK,EACAxK,KAAKsD,MAAMwQ,UACX9T,KAAKsD,MAAM4N,gBAIb,OAAM+D,GAAAC,EAAAlV,KAAKuT,cAAcpK,QAAOyI,gBAAhC,EAAMqD,EACJzN,KAAAA,OAAAA,EACAgD,EACAxK,KAAKsD,MAAMwQ,UACX9T,KAAKsD,MAAM4N,QACXlR,aAGI,OAANmV,UAAWjS,SAAQ0O,gBAAb,EAAAuD,EAAAlP,KAAAmP,OACJ5N,EACAgD,EACAxK,KAAKsD,MAAMwQ,UACX9T,KAAKsD,MAAM4N,UAEP1G,EACE,QACRxK,KAAK0O,SAAS,CAAE/L,KAAM,QAAS6H,MAAOA,MAKpCkE,SAASyD,GA4DfnS,KAAKsD,MA1DHA,KAEA,OAAQ6O,EAAOxP,MACb,IAAK,SACH,MAAO,IACFW,EACHoF,aAAcyJ,EAAOzJ,aACrB2M,cAAelD,EAAO3H,OAE1B,IAAK,QACH,MAAO,IACFlH,EACHgS,UAAU,GAEd,IAAK,WACH,MAAO,IACFhS,EACHgS,UAAU,GAEd,IAAK,UACH,MAAO,IACFhS,EACH4N,QAASiB,EAAOjB,QAChBtK,UAAMY,EACNkB,aAAc,EACd2M,cAAe,KACf7K,MAAO,KACP8K,UAAW1M,EAAS5I,KAAKkD,QAAQ2F,aACjCjF,OAAQ,UACRkQ,UAAW3B,EAAO2B,WAEtB,IAAK,UACH,MAAO,IACFxQ,EACHsD,KAAMuL,EAAOvL,KACb8B,aAAc,EACd2M,cAAe,KACf7K,MAAO,KACP5G,OAAQ,UACR0R,UAAU,GAEd,IAAK,QACH,MAAO,IACFhS,EACHsD,UAAMY,EACNgD,MAAO2H,EAAO3H,MACd9B,aAAcpF,EAAMoF,aAAe,EACnC2M,cAAelD,EAAO3H,MACtB8K,UAAU,EACV1R,OAAQ,SAEZ,IAAK,WACH,MAAO,IACFN,KACA6O,EAAO7O,SAILgP,CAAQtS,KAAKsD,OAE1BmI,EAAcS,OAAM,KAClBlM,KAAKsN,UAAU1F,SAASqH,IACtBA,EAASsG,iBAAiBpD,MAE5BnS,KAAKuT,cAAcxD,OAAO,CACxBvM,SAAUxD,KACV2C,KAAM,UACNwP,eAMD,SAAS9D,IAMd,MAAO,CACL6C,aAAS1J,EACTZ,UAAMY,EACNgD,MAAO,KACP9B,aAAc,EACd2M,cAAe,KACfC,UAAU,EACV1R,OAAQ,OACRkQ,eAAWtM,GCzSR,MAAMgO,UAAsB1V,EAOjCC,YAAYoJ,GACVnC,QACAhH,KAAKmJ,OAASA,GAAU,GACxBnJ,KAAKyV,UAAY,GACjBzV,KAAKsT,WAAa,EAGpBX,MACEC,EACA1P,EACAI,GAEA,MAAME,EAAW,IAAI6P,EAAS,CAC5BE,cAAevT,KACfwN,OAAQoF,EAAOE,YACfQ,aAActT,KAAKsT,WACnBpQ,QAAS0P,EAAO8C,uBAAuBxS,GACvCI,QACA8J,eAAgBlK,EAAQQ,YACpBkP,EAAO+C,oBAAoBzS,EAAQQ,kBACnC8D,IAKN,OAFAxH,KAAKO,IAAIiD,GAEFA,EAGTjD,IAAIiD,GACFxD,KAAKyV,UAAU1J,KAAKvI,GACpBxD,KAAK+P,OAAO,CAAEpN,KAAM,QAASa,aAG/B+K,OAAO/K,GACLxD,KAAKyV,UAAYzV,KAAKyV,UAAUnU,QAAQC,GAAMA,IAAMiC,IACpDxD,KAAK+P,OAAO,CAAEpN,KAAM,UAAWa,aAGjC0P,QACEzH,EAAcS,OAAM,KAClBlM,KAAKyV,UAAU7N,SAASpE,IACtBxD,KAAKuO,OAAO/K,SAKlB2P,SACE,OAAOnT,KAAKyV,UAGdjG,KACE/M,GAMA,YAJ6B,IAAlBA,EAAQG,QACjBH,EAAQG,OAAQ,GAGX5C,KAAKyV,UAAUjG,MAAMhM,GAAaD,EAAcd,EAASe,KAGlE4P,QAAQ3Q,GACN,OAAOzC,KAAKyV,UAAUnU,QAAQkC,GAAaD,EAAcd,EAASe,KAGpEuM,OAAO5H,GACLsD,EAAcS,OAAM,KAClBlM,KAAKC,UAAU2H,SAAQ,EAAGvH,eACxBA,EAAS8H,SAKfyN,wBAA0C,IAAAC,EAgBxC,OAfA7V,KAAK8V,UAAW,OAACD,EAAA7V,KAAK8V,UAAND,EAAkBzP,QAAQC,WACvCI,MAAK,KACJ,MAAMsP,EAAkB/V,KAAKyV,UAAUnU,QAAQC,GAAMA,EAAE+B,MAAMgS,WAC7D,OAAO7J,EAAcS,OAAM,IACzB6J,EAAgBzR,QACd,CAACmF,EAASjG,IACRiG,EAAQhD,MAAK,IAAMjD,EAAS4H,WAAWX,MAAM1J,MAC/CqF,QAAQC,gBAIbI,MAAK,KACJzG,KAAK8V,cAAWtO,KAGbxH,KAAK8V,UCzKT,SAASE,IAKd,MAAO,CACL1E,QAAUJ,IACRA,EAAQC,QAAU,KAAM,IAAAd,EAAAe,EAAA6E,EAAAC,EAAAC,EAAAC,EACtB,MAAMC,SACJnF,EAAAA,EAAQf,eAAR,SAAAE,EAAsB/B,aAAtB8C,EAA4BiF,YACxBC,SAAYpF,EAAAA,EAAQf,eAAR,SAAA8F,EAAsB3H,aAAtB4H,EAA4BI,UACxC3F,EAAY2F,MAAAA,OAAAA,EAAAA,EAAW3F,UACvB4F,EAA8C,aAAhB,MAATD,SAAAA,EAAWE,WAChCC,EAAkD,cAAhB,MAATH,SAAAA,EAAWE,WACpCE,GAAW,OAAAP,EAAAjF,EAAQ5N,MAAMsD,WAAd,EAAAuP,EAAoBQ,QAAS,GACxCC,GAAgB,OAAAR,EAAAlF,EAAQ5N,MAAMsD,WAAd,EAAAwP,EAAoBS,aAAc,GACxD,IAAIC,EAAgBF,EAChBG,GAAY,EAEhB,MAiBMzU,EACJ4O,EAAQhO,QAAQZ,SACf,KACC8D,QAAQ0D,wCAC2BoH,EAAQhO,QAAQF,gBAGjDgU,EAAgB,CACpBL,EACAM,EACAC,EACAC,KAEAL,EAAgBK,EACZ,CAACF,KAAUH,GACX,IAAIA,EAAeG,GAChBE,EAAW,CAACD,KAASP,GAAS,IAAIA,EAAOO,IAI5CE,EAAY,CAChBT,EACAhI,EACAsI,EACAE,KAEA,GAAIJ,EACF,OAAO3Q,QAAQ0D,OAAO,aAGxB,QAAqB,IAAVmN,IAA0BtI,GAAUgI,EAAM1R,OACnD,OAAOmB,QAAQC,QAAQsQ,GAGzB,MAAMjG,EAAuC,CAC3CrO,SAAU6O,EAAQ7O,SAClBsO,UAAWsG,EACX3I,KAAM4C,EAAQhO,QAAQoL,MAtDCuC,QAyDPH,EAxDlBvM,OAAO2M,eAAeD,EAAQ,SAAU,CACtCE,YAAY,EACZC,IAAK,KAAM,IAAAqG,EAGFC,EAKP,OAPI,OAAJD,EAAInG,EAAQD,SAARoG,EAAgBE,QAClBR,GAAY,EAEZ,OAAA7F,EAAAA,EAAQD,SAARqG,EAAgBnQ,iBAAiB,SAAS,KACxC4P,GAAY,KAGT7F,EAAQD,UAgDnB,MAAMuG,EAAgBlV,EAAQoO,GAM9B,OAJgBtK,QAAQC,QAAQmR,GAAe/Q,MAAMyQ,GACnDF,EAAcL,EAAOM,EAAOC,EAAMC,MAMtC,IAAI1N,EAGJ,GAAKiN,EAASzR,OAKT,GAAIsR,EAAoB,CAC3B,MAAM5H,OAA8B,IAAdgC,EAChBsG,EAAQtI,EACVgC,EACA8G,EAAiBvG,EAAQhO,QAASwT,GACtCjN,EAAU2N,EAAUV,EAAU/H,EAAQsI,QAInC,GAAIR,EAAwB,CAC/B,MAAM9H,OAA8B,IAAdgC,EAChBsG,EAAQtI,EACVgC,EACA+G,EAAqBxG,EAAQhO,QAASwT,GAC1CjN,EAAU2N,EAAUV,EAAU/H,EAAQsI,GAAO,OAI1C,CACHH,EAAgB,GAEhB,MAAMnI,OAAqD,IAArCuC,EAAQhO,QAAQuU,iBAQtChO,GALE4M,IAAeK,EAAS,IACpBL,EAAYK,EAAS,GAAI,EAAGA,GAK9BU,EAAU,GAAIzI,EAAQiI,EAAc,IACpCxQ,QAAQC,QAAQ2Q,EAAc,GAAIJ,EAAc,GAAIF,EAAS,KAGjE,IAAK,IAAIpR,EAAI,EAAGA,EAAIoR,EAASzR,OAAQK,IACnCmE,EAAUA,EAAQhD,MAAMkQ,IAMtB,IAJEN,IAAeK,EAASpR,IACpB+Q,EAAYK,EAASpR,GAAIA,EAAGoR,GAGT,CACvB,MAAMO,EAAQtI,EACViI,EAActR,GACdmS,EAAiBvG,EAAQhO,QAASyT,GACtC,OAAOS,EAAUT,EAAOhI,EAAQsI,GAElC,OAAO7Q,QAAQC,QACb2Q,EAAcL,EAAOC,EAActR,GAAIoR,EAASpR,aApDtDmE,EAAU2N,EAAU,IA+DtB,OALqB3N,EAAQhD,MAAMkQ,IAAW,CAC5CA,QACAE,WAAYC,SASf,SAASW,EACdvU,EACAyT,GAEA,aAAOzT,EAAQuU,wBAARvU,EAAQuU,iBAAmBd,EAAMA,EAAM1R,OAAS,GAAI0R,GAGtD,SAASe,EACdxU,EACAyT,GAEA,OAAA,MAAOzT,EAAQwU,0BAAf,EAAOxU,EAAQwU,qBAAuBf,EAAM,GAAIA,GAO3C,SAASgB,EACdzU,EACAyT,GAEA,GAAIzT,EAAQuU,kBAAoBjS,MAAMC,QAAQkR,GAAQ,CACpD,MAAMiB,EAAgBH,EAAiBvU,EAASyT,GAChD,OACE,MAAOiB,IAEW,IAAlBA,GAUC,SAASC,EACd3U,EACAyT,GAEA,GAAIzT,EAAQwU,sBAAwBlS,MAAMC,QAAQkR,GAAQ,CACxD,MAAMmB,EAAoBJ,EAAqBxU,EAASyT,GACxD,OACE,MAAOmB,IAEe,IAAtBA,GC/KC,MAAMC,UAMHjY,EA8BRC,YACE6S,EACA1P,GAQA8D,QAEAhH,KAAK4S,OAASA,EACd5S,KAAKkD,QAAUA,EACflD,KAAKgY,aAAe,IAAI9X,IACxBF,KAAKiY,YAAc,KACnBjY,KAAKkY,cACLlY,KAAKqN,WAAWnK,GAGRgV,cACRlY,KAAKuO,OAASvO,KAAKuO,OAAOnO,KAAKJ,MAC/BA,KAAK0P,QAAU1P,KAAK0P,QAAQtP,KAAKJ,MAGzBQ,cACoB,IAAxBR,KAAKC,UAAUW,OACjBZ,KAAKmY,aAAarI,YAAY9P,MAE1BoY,EAAmBpY,KAAKmY,aAAcnY,KAAKkD,UAC7ClD,KAAKqY,eAGPrY,KAAKsY,gBAIC5X,gBACHV,KAAKW,gBACRX,KAAKyM,UAIToD,yBACE,OAAO0I,EACLvY,KAAKmY,aACLnY,KAAKkD,QACLlD,KAAKkD,QAAQsV,oBAIjB/I,2BACE,OAAO8I,EACLvY,KAAKmY,aACLnY,KAAKkD,QACLlD,KAAKkD,QAAQuV,sBAIjBhM,UACEzM,KAAKC,UAAY,IAAIC,IACrBF,KAAK0Y,oBACL1Y,KAAK2Y,uBACL3Y,KAAKmY,aAAanI,eAAehQ,MAGnCqN,WACEnK,EAOA0V,GAEA,MAAMC,EAAc7Y,KAAKkD,QACnB4V,EAAY9Y,KAAKmY,aAuBvB,GArBAnY,KAAKkD,QAAUlD,KAAK4S,OAAOG,oBAAoB7P,GAa1CqC,EAAoBsT,EAAa7Y,KAAKkD,UACzClD,KAAK4S,OAAOmG,gBAAgBhJ,OAAO,CACjCpN,KAAM,yBACND,MAAO1C,KAAKmY,aACZlJ,SAAUjP,YAKoB,IAAzBA,KAAKkD,QAAQgM,SACY,kBAAzBlP,KAAKkD,QAAQgM,QAEpB,MAAM,IAAIgD,MAAM,oCAIblS,KAAKkD,QAAQb,WAChBrC,KAAKkD,QAAQb,SAAWwW,EAAYxW,UAGtCrC,KAAKgZ,cAEL,MAAMC,EAAUjZ,KAAKW,eAInBsY,GACAC,EACElZ,KAAKmY,aACLW,EACA9Y,KAAKkD,QACL2V,IAGF7Y,KAAKqY,eAIPrY,KAAKmZ,aAAaP,IAIhBK,GACCjZ,KAAKmY,eAAiBW,GACrB9Y,KAAKkD,QAAQgM,UAAY2J,EAAY3J,SACrClP,KAAKkD,QAAQvB,YAAckX,EAAYlX,WAEzC3B,KAAKoZ,qBAGP,MAAMC,EAAsBrZ,KAAKsZ,0BAI/BL,GACCjZ,KAAKmY,eAAiBW,GACrB9Y,KAAKkD,QAAQgM,UAAY2J,EAAY3J,SACrCmK,IAAwBrZ,KAAKuZ,wBAE/BvZ,KAAKwZ,sBAAsBH,GAI/BI,oBACEvW,GAQA,MAAMR,EAAQ1C,KAAK4S,OAAOmG,gBAAgBpG,MAAM3S,KAAK4S,OAAQ1P,GAEvDqB,EAASvE,KAAK0Z,aAAahX,EAAOQ,GAuBxC,OAqhBJ,SAOE+L,EACA0K,EACAzW,GAcA,GAAIA,EAAQ0W,iBACV,OAAO,EAKT,QAAgCpS,IAA5BtE,EAAQ2W,gBAIV,OAAOF,EAAiBG,kBAK1B,IAAKvU,EAAoB0J,EAASI,mBAAoBsK,GACpD,OAAO,EAIT,OAAO,EArlBDI,CAAsC/Z,KAAMuE,EAAQrB,KAiBtDlD,KAAKga,cAAgBzV,EACrBvE,KAAKia,qBAAuBja,KAAKkD,QACjClD,KAAKka,mBAAqBla,KAAKmY,aAAa7U,OAEvCiB,EAGT8K,mBACE,OAAOrP,KAAKga,cAGdG,YACE5V,GAEA,MAAM6V,EAAgB,GAatB,OAXAjW,OAAOC,KAAKG,GAAQqD,SAASpD,IAC3BL,OAAO2M,eAAesJ,EAAe5V,EAAK,CACxC6V,cAAc,EACdtJ,YAAY,EACZC,IAAK,KACHhR,KAAKgY,aAAazX,IAAIiE,GACfD,EAAOC,SAKb4V,EAGTE,kBACE,OAAOta,KAAKmY,aAGd5J,SACEvO,KAAK4S,OAAOmG,gBAAgBxK,OAAOvO,KAAKmY,cAG1CzI,SAAmB2G,YACjBA,KACGnT,GAC+C,IAGlD,OAAOlD,KAAKkQ,MAAM,IACbhN,EACHoL,KAAM,CAAE+H,iBAIZkE,gBACErX,GAQA,MAAMsX,EAAmBxa,KAAK4S,OAAOG,oBAAoB7P,GAEnDR,EAAQ1C,KAAK4S,OAChBmG,gBACApG,MAAM3S,KAAK4S,OAAQ4H,GAGtB,OAFA9X,EAAMmP,sBAAuB,EAEtBnP,EAAMwN,QAAQzJ,MAAK,IAAMzG,KAAK0Z,aAAahX,EAAO8X,KAGjDtK,MACRC,GAC6C,IAAAsK,EAC7C,OAAOza,KAAKqY,aAAa,IACpBlI,EACHR,cAA6C,OAA9BQ,EAAAA,EAAaR,gBAAiB8K,IAC5ChU,MAAK,KACNzG,KAAKmZ,eACEnZ,KAAKga,iBAIR3B,aACNlI,GAGAnQ,KAAKgZ,cAGL,IAAIvP,EAA2CzJ,KAAKmY,aAAajI,MAC/DlQ,KAAKkD,QACLiN,GAOF,OAJI,MAACA,GAAAA,EAAcuK,eACjBjR,EAAUA,EAAQgB,MAAM1J,IAGnB0I,EAGD2P,qBAGN,GAFApZ,KAAK0Y,oBAGH7X,GACAb,KAAKga,cAAc3W,UAClBrC,EAAehB,KAAKkD,QAAQvB,WAE7B,OAGF,MAOMwE,EAPO1E,EACXzB,KAAKga,cAAclM,cACnB9N,KAAKkD,QAAQvB,WAKQ,EAEvB3B,KAAK2a,eAAiBrU,YAAW,KAC1BtG,KAAKga,cAAc3W,SACtBrD,KAAKmZ,iBAENhT,GAGGmT,yBAAyB,IAAAsB,EAC/B,MAA+C,mBAAjC5a,KAAKkD,QAAQ2X,gBACvB7a,KAAKkD,QAAQ2X,gBAAgB7a,KAAKga,cAAcpT,KAAM5G,KAAKmY,cADxD,OAEHyC,EAAA5a,KAAKkD,QAAQ2X,kBAFVD,EAKDpB,sBAAsBsB,GAC5B9a,KAAK2Y,uBAEL3Y,KAAKuZ,uBAAyBuB,GAG5Bja,IACyB,IAAzBb,KAAKkD,QAAQgM,SACZlO,EAAehB,KAAKuZ,yBACW,IAAhCvZ,KAAKuZ,yBAKPvZ,KAAK+a,kBAAoBC,aAAY,MAEjChb,KAAKkD,QAAQ+X,6BACblU,EAAac,cAEb7H,KAAKqY,iBAENrY,KAAKuZ,yBAGFjB,eACNtY,KAAKoZ,qBACLpZ,KAAKwZ,sBAAsBxZ,KAAKsZ,0BAG1BZ,oBACF1Y,KAAK2a,iBACP1N,aAAajN,KAAK2a,gBAClB3a,KAAK2a,oBAAiBnT,GAIlBmR,uBACF3Y,KAAK+a,oBACPG,cAAclb,KAAK+a,mBACnB/a,KAAK+a,uBAAoBvT,GAInBkS,aACRhX,EACAQ,GAQA,MAAM4V,EAAY9Y,KAAKmY,aACjBU,EAAc7Y,KAAKkD,QACnBiY,EAAanb,KAAKga,cAGlBoB,EAAkBpb,KAAKka,mBACvBmB,EAAoBrb,KAAKia,qBACzBqB,EAAc5Y,IAAUoW,EACxByC,EAAoBD,EACtB5Y,EAAMY,MACNtD,KAAKwb,yBACHC,EAAkBH,EACpBtb,KAAKga,cACLha,KAAK0b,qBAEHpY,MAAEA,GAAUZ,EAClB,IAGIkE,GAHAkH,cAAEA,EAAFtD,MAAiBA,EAAjBwD,eAAwBA,EAAxBnL,YAAwCA,EAAxCe,OAAqDA,GAAWN,EAChEqY,GAAiB,EACjB7B,GAAoB,EAIxB,GAAI5W,EAAQ0Y,mBAAoB,CAC9B,MAAM3C,EAAUjZ,KAAKW,eAEfkb,GAAgB5C,GAAWb,EAAmB1V,EAAOQ,GAErD4Y,EACJ7C,GAAWC,EAAsBxW,EAAOoW,EAAW5V,EAAS2V,IAE1DgD,GAAgBC,KAClBjZ,EAAc+F,EAASlG,EAAMQ,QAAQ2F,aACjC,WACA,SACCiF,IACHlK,EAAS,YAGsB,gBAA/BV,EAAQ0Y,qBACV/Y,EAAc,QAKlB,GACEK,EAAQ0W,mBACPtW,EAAMwK,eADP,MAEA2N,GAAAA,EAAiBM,WACN,UAAXnY,EAEAgD,EAAO6U,EAAgB7U,KACvBkH,EAAgB2N,EAAgB3N,cAChClK,EAAS6X,EAAgB7X,OACzB+X,GAAiB,OAGd,GAAIzY,EAAQ8Y,aAAgC,IAAf1Y,EAAMsD,KAEtC,GACEuU,GACA7X,EAAMsD,QAASwU,MAAAA,OAAAA,EAAAA,EAAiBxU,OAChC1D,EAAQ8Y,SAAWhc,KAAKic,SAExBrV,EAAO5G,KAAKkc,kBAEZ,IACElc,KAAKic,SAAW/Y,EAAQ8Y,OACxBpV,EAAO1D,EAAQ8Y,OAAO1Y,EAAMsD,MAC5BA,EAAOF,EAAYyU,MAAAA,OAAAA,EAAAA,EAAYvU,KAAMA,EAAM1D,GAC3ClD,KAAKkc,aAAetV,EACpB5G,KAAKiY,YAAc,KACnB,MAAOA,GAIPjY,KAAKiY,YAAcA,OAMvBrR,EAAOtD,EAAMsD,KAIf,QACqC,IAA5B1D,EAAQ2W,sBACC,IAATjT,GACI,YAAXhD,EACA,CACA,IAAIiW,EAGJ,GACE,MAAAsB,GAAAA,EAAYrB,mBACZ5W,EAAQ2W,mBAAR,MAA4BwB,OAA5B,EAA4BA,EAAmBxB,iBAE/CA,EAAkBsB,EAAWvU,UAM7B,GAJAiT,EACqC,mBAA5B3W,EAAQ2W,gBACV3W,EAAQ2W,kBACT3W,EAAQ2W,gBACV3W,EAAQ8Y,aAAqC,IAApBnC,EAC3B,IACEA,EAAkB3W,EAAQ8Y,OAAOnC,GACjC7Z,KAAKiY,YAAc,KACnB,MAAOA,GAIPjY,KAAKiY,YAAcA,OAKM,IAApB4B,IACTjW,EAAS,UACTgD,EAAOF,EAAYyU,MAAAA,OAAAA,EAAAA,EAAYvU,KAAMiT,EAAiB3W,GACtD4W,GAAoB,GAIpB9Z,KAAKiY,cACPzN,EAAQxK,KAAKiY,YACbrR,EAAO5G,KAAKkc,aACZlO,EAAiBlM,KAAKC,MACtB6B,EAAS,SAGX,MAAMuY,EAA6B,aAAhBtZ,EACbuZ,EAAuB,YAAXxY,EACZyY,EAAqB,UAAXzY,EAgChB,MA9BuD,CACrDA,SACAf,cACAuZ,YACAL,UAAsB,YAAXnY,EACXyY,UACAC,iBAAkBF,GAAaD,EAC/BvV,OACAkH,gBACAtD,QACAwD,iBACAtF,aAAcpF,EAAM2K,kBACpBoH,cAAe/R,EAAM4K,mBACrBH,iBAAkBzK,EAAMyK,iBACxBwO,UAAWjZ,EAAMuK,gBAAkB,GAAKvK,EAAMyK,iBAAmB,EACjEyO,oBACElZ,EAAMuK,gBAAkB0N,EAAkB1N,iBAC1CvK,EAAMyK,iBAAmBwN,EAAkBxN,iBAC7CoO,aACAM,aAAcN,IAAeC,EAC7BM,eAAgBL,GAAmC,IAAxB/Y,EAAMwK,cACjCwH,SAA0B,WAAhBzS,EACViX,oBACA6B,iBACAgB,eAAgBN,GAAmC,IAAxB/Y,EAAMwK,cACjCzK,QAASA,EAAQX,EAAOQ,GACxBwM,QAAS1P,KAAK0P,QACdnB,OAAQvO,KAAKuO,QAMjB4K,aAAaP,GACX,MAAMuC,EAAanb,KAAKga,cAIlB4C,EAAa5c,KAAK0Z,aAAa1Z,KAAKmY,aAAcnY,KAAKkD,SAK7D,GAJAlD,KAAKka,mBAAqBla,KAAKmY,aAAa7U,MAC5CtD,KAAKia,qBAAuBja,KAAKkD,QAG7BqC,EAAoBqX,EAAYzB,GAClC,OAGFnb,KAAKga,cAAgB4C,EAGrB,MAAMC,EAAsC,CAAEtP,OAAO,IAmCpB,KAA7B,MAAAqL,OAAA,EAAAA,EAAe3Y,YAjCW,MAC5B,IAAKkb,EACH,OAAO,EAGT,MAAM2B,oBAAEA,GAAwB9c,KAAKkD,QAC/B6Z,EAC2B,mBAAxBD,EACHA,IACAA,EAEN,GAC+B,QAA7BC,IACEA,IAA6B/c,KAAKgY,aAAapX,KAEjD,OAAO,EAGT,MAAMoc,EAAgB,IAAI9c,IAAJ,MACpB6c,EAAAA,EAA4B/c,KAAKgY,cAOnC,OAJIhY,KAAKkD,QAAQ+Z,kBACfD,EAAczc,IAAI,SAGb4D,OAAOC,KAAKpE,KAAKga,eAAepV,MAAMJ,IAC3C,MAAM0Y,EAAW1Y,EAEjB,OADgBxE,KAAKga,cAAckD,KAAc/B,EAAW+B,IAC1CF,EAAcG,IAAID,OAIEE,KACxCP,EAAqB5c,WAAY,GAGnCD,KAAK+P,OAAO,IAAK8M,KAAyBjE,IAGpCI,cACN,MAAMtW,EAAQ1C,KAAK4S,OAAOmG,gBAAgBpG,MAAM3S,KAAK4S,OAAQ5S,KAAKkD,SAElE,GAAIR,IAAU1C,KAAKmY,aACjB,OAGF,MAAMW,EAAY9Y,KAAKmY,aAGvBnY,KAAKmY,aAAezV,EACpB1C,KAAKwb,yBAA2B9Y,EAAMY,MACtCtD,KAAK0b,oBAAsB1b,KAAKga,cAE5Bha,KAAKW,uBACPmY,GAAAA,EAAW9I,eAAehQ,MAC1B0C,EAAMoN,YAAY9P,OAItBuS,cAAcJ,GACZ,MAAMyG,EAA+B,GAEjB,YAAhBzG,EAAOxP,KACTiW,EAAc/O,WAAasI,EAAOxD,OACT,UAAhBwD,EAAOxP,MAAqBsG,EAAiBkJ,EAAO3H,SAC7DoO,EAAc7O,SAAU,GAG1B/J,KAAKmZ,aAAaP,GAEd5Y,KAAKW,gBACPX,KAAKsY,eAIDvI,OAAO6I,GACbnN,EAAcS,OAAM,KAEW,IAAAgI,EAAAS,EAAAL,EAAAH,EAA7B,GAAIyE,EAAc/O,iBAChBqK,GAAAS,EAAA3U,KAAKkD,SAAQ2G,qBAAY7J,KAAKga,cAAcpT,MAC5C,OAAK1D,GAAAA,EAAAA,KAAAA,SAAQ0O,YAAb0C,EAAArO,KAAAkO,EAAyBnU,KAAKga,cAAcpT,KAAO,WAC9C,GAAIgS,EAAc7O,QAAS,CAAA,IAAAgL,EAAAR,EAAAY,EAAAH,SAChCD,GAAAR,EAAAvU,KAAKkD,SAAQ6G,mBAAU/J,KAAKga,cAAcxP,OAC1C,OAAKtH,GAAAA,EAAAA,KAAAA,SAAQ0O,YAAbuD,EAAAlP,KAAA+O,OAAyBxN,EAAWxH,KAAKga,cAAcxP,OAIrDoO,EAAc3Y,WAChBD,KAAKC,UAAU2H,SAAQ,EAAGvH,eACxBA,EAASL,KAAKga,kBAKdpB,EAAcrL,OAChBvN,KAAK4S,OAAOmG,gBAAgBhJ,OAAO,CACjCrN,MAAO1C,KAAKmY,aACZxV,KAAM,+BAkBhB,SAASyV,EACP1V,EACAQ,GAEA,OAfF,SACER,EACAQ,GAEA,SACsB,IAApBA,EAAQgM,SACPxM,EAAMY,MAAMwK,eACY,UAAvBpL,EAAMY,MAAMM,SAA+C,IAAzBV,EAAQma,cAS5CC,CAAkB5a,EAAOQ,IACxBR,EAAMY,MAAMwK,cAAgB,GAC3ByK,EAAc7V,EAAOQ,EAASA,EAAQqa,gBAI5C,SAAShF,EACP7V,EACAQ,EACAsa,GAIA,IAAwB,IAApBta,EAAQgM,QAAmB,CAC7B,MAAMjO,EAAyB,mBAAVuc,EAAuBA,EAAM9a,GAAS8a,EAE3D,MAAiB,WAAVvc,IAAiC,IAAVA,GAAmBoC,EAAQX,EAAOQ,GAElE,OAAO,EAGT,SAASgW,EACPxW,EACAoW,EACA5V,EACA2V,GAEA,OACsB,IAApB3V,EAAQgM,UACPxM,IAAUoW,IAAqC,IAAxBD,EAAY3J,YAClChM,EAAQua,UAAmC,UAAvB/a,EAAMY,MAAMM,SAClCP,EAAQX,EAAOQ,GAInB,SAASG,EACPX,EACAQ,GAEA,OAAOR,EAAM4M,cAAcpM,EAAQvB,WCttB9B,SAAS+b,EAA+Bla,GAC7C,OAAOA,EAASF,MAAMgS,SAGjB,SAASqI,EAA4Bjb,GAC1C,MAA8B,YAAvBA,EAAMY,MAAMM,kDCjDd,cAMGmU,EAqBRhY,YACE6S,EACA1P,GAQA8D,MAAM4L,EAAQ1P,GAGNgV,cACRlR,MAAMkR,cACNlY,KAAK4d,cAAgB5d,KAAK4d,cAAcxd,KAAKJ,MAC7CA,KAAK6d,kBAAoB7d,KAAK6d,kBAAkBzd,KAAKJ,MAGvDqN,WACEnK,EAOA0V,GAEA5R,MAAMqG,WACJ,IACKnK,EACHmO,SAAU2E,KAEZ4C,GAIJa,oBACEvW,GASA,OADAA,EAAQmO,SAAW2E,IACZhP,MAAMyS,oBAAoBvW,GAMnC0a,eAAcjN,UAAEA,KAAczN,GAAkC,IAG9D,OAAOlD,KAAKkQ,MAAM,IACbhN,EACHoL,KAAM,CACJgI,UAAW,CAAEE,UAAW,UAAW7F,gBAKzCkN,mBAAkBlN,UAChBA,KACGzN,GACyB,IAG5B,OAAOlD,KAAKkQ,MAAM,IACbhN,EACHoL,KAAM,CACJgI,UAAW,CAAEE,UAAW,WAAY7F,gBAKhC+I,aACRhX,EACAQ,GAO4C,IAAA4a,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC5C,MAAM7a,MAAEA,GAAUZ,EACZ6B,EAASyC,MAAM0S,aAAahX,EAAOQ,IAEnCiZ,WAAEA,EAAFM,aAAcA,GAAiBlY,EAE/BgS,EACJ4F,GAAwD,aAAzB7F,OAAjBhT,EAAAA,EAAM6K,YAAsBqI,OAAXF,EAAAA,EAAAA,gBAAAA,EAAWE,EAAAA,WAEtCC,EACJ0F,GAAwD,cAAzB7F,OAAjBhT,EAAAA,EAAM6K,YAAsBqI,OAAXF,EAAAA,EAAAA,gBAAAA,EAAWE,EAAAA,WAE5C,MAAO,IACFjS,EACHqZ,cAAe5d,KAAK4d,cACpBC,kBAAmB7d,KAAK6d,kBACxBlG,YAAaA,EAAYzU,EAAD,OAAAgb,EAAU5a,EAAMsD,WAAhB,EAAUsX,EAAYvH,OAC9CkB,gBAAiBA,EAAgB3U,EAAD,OAAAib,EAAU7a,EAAMsD,WAAhB,EAAUuX,EAAYxH,OACtDJ,qBACAE,yBACAgG,aACEA,IAAiBlG,IAAuBE,0CCpIzC,cAKG3W,EAeRC,YACE6S,EACA1P,GAEA8D,QAEAhH,KAAK4S,OAASA,EACd5S,KAAKqN,WAAWnK,GAChBlD,KAAKkY,cACLlY,KAAKmZ,eAGGjB,cACRlY,KAAKoe,OAASpe,KAAKoe,OAAOhe,KAAKJ,MAC/BA,KAAKgP,MAAQhP,KAAKgP,MAAM5O,KAAKJ,MAG/BqN,WACEnK,GACA,IAAAmb,EACA,MAAMxF,EAAc7Y,KAAKkD,QACzBlD,KAAKkD,QAAUlD,KAAK4S,OAAO8C,uBAAuBxS,GAC7CqC,EAAoBsT,EAAa7Y,KAAKkD,UACzClD,KAAK4S,OAAO0L,mBAAmBvO,OAAO,CACpCpN,KAAM,yBACNa,SAAUxD,KAAKue,gBACftP,SAAUjP,OAGd,OAAAqe,EAAAre,KAAKue,kBAALF,EAAsBhR,WAAWrN,KAAKkD,SAG9BxC,gBACkB,IAAA8d,EAArBxe,KAAKW,iBACR,OAAA6d,EAAAxe,KAAKue,kBAALC,EAAsBxO,eAAehQ,OAIzCuV,iBAAiBpD,GACfnS,KAAKmZ,eAGL,MAAMP,EAA+B,CACnC3Y,WAAW,GAGO,YAAhBkS,EAAOxP,KACTiW,EAAc/O,WAAY,EACD,UAAhBsI,EAAOxP,OAChBiW,EAAc7O,SAAU,GAG1B/J,KAAK+P,OAAO6I,GAGdvJ,mBAME,OAAOrP,KAAKga,cAGdhL,QACEhP,KAAKue,qBAAkB/W,EACvBxH,KAAKmZ,eACLnZ,KAAK+P,OAAO,CAAE9P,WAAW,IAG3Bme,OACEtK,EACA5Q,GAgBA,OAdAlD,KAAKye,cAAgBvb,EAEjBlD,KAAKue,iBACPve,KAAKue,gBAAgBvO,eAAehQ,MAGtCA,KAAKue,gBAAkBve,KAAK4S,OAAO0L,mBAAmB3L,MAAM3S,KAAK4S,OAAQ,IACpE5S,KAAKkD,QACR4Q,eACuB,IAAdA,EAA4BA,EAAY9T,KAAKkD,QAAQ4Q,YAGhE9T,KAAKue,gBAAgBzO,YAAY9P,MAE1BA,KAAKue,gBAAgB9K,UAGtB0F,eACN,MAAM7V,EAAQtD,KAAKue,gBACfve,KAAKue,gBAAgBjb,MNwOpB,CACL4N,aAAS1J,EACTZ,UAAMY,EACNgD,MAAO,KACP9B,aAAc,EACd2M,cAAe,KACfC,UAAU,EACV1R,OAAQ,OACRkQ,eAAWtM,GM7OL4U,EAA6B,YAAjB9Y,EAAMM,OAClBW,EAKF,IACCjB,EACH8Y,YACAsC,UAAWtC,EACXL,UAA4B,YAAjBzY,EAAMM,OACjByY,QAA0B,UAAjB/Y,EAAMM,OACf+a,OAAyB,SAAjBrb,EAAMM,OACdwa,OAAQpe,KAAKoe,OACbpP,MAAOhP,KAAKgP,OAGdhP,KAAKga,cAAgBzV,EAQfwL,OAAO7M,GACbuI,EAAcS,OAAM,KAGO,IAAA0S,EAAAC,EAAAC,EAAAC,EADzB,GAAI/e,KAAKye,eAAiBze,KAAKW,eAC7B,GAAIuC,EAAQ2G,UAER,OADF+U,GAAAC,EAAA7e,KAAKye,eAAc5U,YACjB+U,EAAA3Y,KAAA4Y,EAAA7e,KAAKga,cAAcpT,KACnB5G,KAAKga,cAAclG,UACnB9T,KAAKga,cAAc9I,gBAErB4N,GAAAC,EAAA/e,KAAKye,eAAc7M,qBACjB5R,KAAKga,cAAcpT,KACnB,KACA5G,KAAKga,cAAclG,UACnB9T,KAAKga,cAAc9I,cAEhB,GAAIhO,EAAQ6G,QAAS,CAAA,IAAAiV,EAAAC,EAAAC,EAAAC,EAExB,OADFH,GAAAC,EAAAjf,KAAKye,eAAc1U,UACjBiV,EAAA/Y,KAAAgZ,EAAAjf,KAAKga,cAAcxP,MACnBxK,KAAKga,cAAclG,UACnB9T,KAAKga,cAAc9I,gBAErBgO,GAAAC,EAAAnf,KAAKye,eAAc7M,0BACjBpK,EACAxH,KAAKga,cAAcxP,MACnBxK,KAAKga,cAAclG,UACnB9T,KAAKga,cAAc9I,SAMrBhO,EAAQjD,WACVD,KAAKC,UAAU2H,SAAQ,EAAGvH,eACxBA,EAASL,KAAKga,yCC5LjB,cAA8Bla,EAOnCC,YAAY6S,EAAqBH,GAC/BzL,QAEAhH,KAAK4S,OAASA,EACd5S,KAAKyS,QAAU,GACfzS,KAAKuE,OAAS,GACdvE,KAAKsN,UAAY,GACjBtN,KAAKof,aAAe,GAEhB3M,GACFzS,KAAKqf,WAAW5M,GAIVjS,cACoB,IAAxBR,KAAKC,UAAUW,MACjBZ,KAAKsN,UAAU1F,SAASqH,IACtBA,EAAS9O,WAAWoE,IAClBvE,KAAKsf,SAASrQ,EAAU1K,SAMtB7D,gBACHV,KAAKC,UAAUW,MAClBZ,KAAKyM,UAITA,UACEzM,KAAKC,UAAY,IAAIC,IACrBF,KAAKsN,UAAU1F,SAASqH,IACtBA,EAASxC,aAIb4S,WACE5M,EACAmG,GAEA5Y,KAAKyS,QAAUA,EAEfhH,EAAcS,OAAM,KAClB,MAAMqT,EAAgBvf,KAAKsN,UAErBkS,EAAqBxf,KAAKyf,sBAAsBzf,KAAKyS,SAG3D+M,EAAmB5X,SAAS8X,GAC1BA,EAAMzQ,SAAS5B,WAAWqS,EAAMC,sBAAuB/G,KAGzD,MAAMgH,EAAeJ,EAAmBK,KAAKH,GAAUA,EAAMzQ,WACvD6Q,EAAkB3b,OAAO4b,YAC7BH,EAAaC,KAAK5Q,GAAa,CAACA,EAAS/L,QAAQF,UAAWiM,MAExD+Q,EAAYJ,EAAaC,KAAK5Q,GAClCA,EAASI,qBAGL4Q,EAAiBL,EAAahb,MAClC,CAACqK,EAAUiR,IAAUjR,IAAasQ,EAAcW,MAE9CX,EAActa,SAAW2a,EAAa3a,QAAWgb,KAIrDjgB,KAAKsN,UAAYsS,EACjB5f,KAAKof,aAAeU,EACpB9f,KAAKuE,OAASyb,EAEThgB,KAAKW,iBAIVQ,EAAWoe,EAAeK,GAAchY,SAASqH,IAC/CA,EAASxC,aAGXtL,EAAWye,EAAcL,GAAe3X,SAASqH,IAC/CA,EAAS9O,WAAWoE,IAClBvE,KAAKsf,SAASrQ,EAAU1K,SAI5BvE,KAAK+P,cAITV,mBACE,OAAOrP,KAAKuE,OAGd4b,aACE,OAAOngB,KAAKsN,UAAUuS,KAAK5Q,GAAaA,EAASqL,oBAGnD8F,eACE,OAAOpgB,KAAKsN,UAGdmM,oBAAoBhH,GAClB,OAAOzS,KAAKyf,sBAAsBhN,GAASoN,KAAKH,GAC9CA,EAAMzQ,SAASwK,oBAAoBiG,EAAMC,yBAIrCF,sBACNhN,GAEA,MAAM8M,EAAgBvf,KAAKsN,UACrB+S,EAAmB,IAAIC,IAC3Bf,EAAcM,KAAK5Q,GAAa,CAACA,EAAS/L,QAAQF,UAAWiM,MAGzD0Q,EAAwBlN,EAAQoN,KAAK3c,GACzClD,KAAK4S,OAAOG,oBAAoB7P,KAG5Bqd,EACJZ,EAAsBa,SAAShG,IAC7B,MAAMkF,EAAQW,EAAiBrP,IAAIwJ,EAAiBxX,WACpD,OAAa,MAAT0c,EACK,CAAC,CAAEC,sBAAuBnF,EAAkBvL,SAAUyQ,IAExD,MAGLe,EAAqB,IAAIvgB,IAC7BqgB,EAAkBV,KAAKH,GAAUA,EAAMC,sBAAsB3c,aAEzD0d,EAAmBf,EAAsBre,QAC5CkZ,IAAsBiG,EAAmBtD,IAAI3C,EAAiBxX,aAG3D2d,EAAuB,IAAIzgB,IAC/BqgB,EAAkBV,KAAKH,GAAUA,EAAMzQ,YAEnC2R,EAAqBrB,EAAcje,QACtCuf,IAAkBF,EAAqBxD,IAAI0D,KAGxCC,EAAe5d,IACnB,MAAMsX,EAAmBxa,KAAK4S,OAAOG,oBAAoB7P,GACnD6d,EAAkB/gB,KAAKof,aAAa5E,EAAiBxX,WAC3D,OAAO+d,MAAAA,EAAAA,EAAmB,IAAIhJ,EAAc/X,KAAK4S,OAAQ4H,IAGrDwG,EAA6CN,EAAiBb,KAClE,CAAC3c,EAASgd,KACR,GAAIhd,EAAQ0W,iBAAkB,CAE5B,MAAMqH,EAAyBL,EAAmBV,GAClD,QAA+B1Y,IAA3ByZ,EACF,MAAO,CACLtB,sBAAuBzc,EACvB+L,SAAUgS,GAIhB,MAAO,CACLtB,sBAAuBzc,EACvB+L,SAAU6R,EAAY5d,OAY5B,OAAOqd,EACJW,OAAOF,GACP3c,MATiC,CAClCI,EACAC,IAEAib,EAAsBwB,QAAQ1c,EAAEkb,uBAChCA,EAAsBwB,QAAQzc,EAAEib,yBAO5BL,SAASrQ,EAAyB1K,GACxC,MAAM2b,EAAQlgB,KAAKsN,UAAU6T,QAAQlS,IACtB,IAAXiR,IACFlgB,KAAKuE,OhB/GJ,SAAsBO,EAAYob,EAAejf,GACtD,MAAMmE,EAAON,EAAMsc,MAAM,GAEzB,OADAhc,EAAK8a,GAASjf,EACPmE,EgB4GWic,CAAUrhB,KAAKuE,OAAQ2b,EAAO3b,GAC5CvE,KAAK+P,UAIDA,SACNtE,EAAcS,OAAM,KAClBlM,KAAKC,UAAU2H,SAAQ,EAAGvH,eACxBA,EAASL,KAAKuE,uDC1Jf,MAWLxE,YAAYoJ,EAA4B,IACtCnJ,KAAKshB,WAAanY,EAAOmY,YAAc,IAAI9O,EAC3CxS,KAAKuT,cAAgBpK,EAAOoK,eAAiB,IAAIiC,EACjDxV,KAAKwN,OAASrE,EAAOqE,QAAUjC,EAC/BvL,KAAKoN,eAAiBjE,EAAOiE,gBAAkB,GAC/CpN,KAAKuhB,cAAgB,GACrBvhB,KAAKwhB,iBAAmB,GACxBxhB,KAAKyhB,WAAa,EASpBC,QACE1hB,KAAKyhB,aACmB,IAApBzhB,KAAKyhB,aAETzhB,KAAK2hB,iBAAmB5a,EAAa5G,WAAU,KACzC4G,EAAac,cACf7H,KAAK4V,wBACL5V,KAAKshB,WAAWpa,cAGpBlH,KAAK4hB,kBAAoB3Z,EAAc9H,WAAU,KAC3C8H,EAAcK,aAChBtI,KAAK4V,wBACL5V,KAAKshB,WAAWpZ,gBAKtB2Z,UAAgB,IAAAC,EAAAC,EACd/hB,KAAKyhB,aACmB,IAApBzhB,KAAKyhB,aAET,OAAAK,EAAA9hB,KAAK2hB,mBAALG,EAAA7b,KAAAjG,MACAA,KAAK2hB,sBAAmBna,EAExB,OAAAua,EAAA/hB,KAAK4hB,oBAALG,EAAA9b,KAAAjG,MACAA,KAAK4hB,uBAAoBpa,GAc3B2U,WAAWla,EAAgCC,GACzC,MAAOO,GAAWF,EAAgBN,EAAMC,GAExC,OADAO,EAAQI,YAAc,WACf7C,KAAKshB,WAAWlO,QAAQ3Q,GAASwC,OAG1C+c,WAAWvf,GACT,OAAOzC,KAAKuT,cAAcH,QAAQ,IAAK3Q,EAASgB,UAAU,IAAQwB,OAgBpEgd,aACE5f,EACAI,GAC0B,IAAAyf,EAC1B,OAAA,OAAAA,EAAOliB,KAAKshB,WAAW9R,KAAmBnN,EAAUI,SAApD,EAAOyf,EAAuD5e,MAAMsD,KAgDtEub,gBAMElgB,EAMAC,EAGAC,GAEA,MAAMigB,EAAgBpgB,EAAeC,EAAMC,EAAMC,GAC3CkgB,EAAariB,KAAKiiB,aAAoBG,EAAc/f,UAE1D,OAAOggB,EACHjc,QAAQC,QAAQgc,GAChBriB,KAAKsiB,WAAWF,GAetBG,eACEC,GAEA,OAAOxiB,KAAK+Y,gBACT3F,QAAQoP,GACR3C,KAAI,EAAGxd,WAAUiB,WAET,CAACjB,EADKiB,EAAMsD,QAKzB6b,aACEpgB,EACAqgB,EACAxf,GAEA,MAAMR,EAAQ1C,KAAKshB,WAAW9R,KAAmBnN,GAE3CuE,EjBnLH,SACL8b,EACAC,GAEA,MAA0B,mBAAZD,EACTA,EAAgDC,GACjDD,EiB6KWE,CAAiBF,EADhB,MAAGhgB,OAAH,EAAGA,EAAOY,MAAMsD,MAG9B,QAAoB,IAATA,EACT,OAGF,MAAMwb,EAAgBpgB,EAAeK,GAC/BmY,EAAmBxa,KAAK+S,oBAAoBqP,GAClD,OAAOpiB,KAAKshB,WACT3O,MAAM3S,KAAMwa,GACZhM,QAAQ5H,EAAM,IAAK1D,EAASyL,QAAQ,IAmBzCkU,eACEL,EACAE,EACAxf,GAEA,OAAOuI,EAAcS,OAAM,IACzBlM,KAAK+Y,gBACF3F,QAAQoP,GACR3C,KAAI,EAAGxd,cAAe,CACrBA,EACArC,KAAKyiB,aAA2BpgB,EAAUqgB,EAASxf,QAK3D4f,cACEzgB,EAIAI,GAC8C,IAAAsgB,EAC9C,OAAO,OAAAA,EAAA/iB,KAAKshB,WAAW9R,KAA2BnN,EAAUI,SAArD,EAAAsgB,EAA+Dzf,MAcxE0f,cACE/gB,EACAC,GAEA,MAAOO,GAAWF,EAAgBN,EAAMC,GAClCof,EAAathB,KAAKshB,WACxB7V,EAAcS,OAAM,KAClBoV,EAAWlO,QAAQ3Q,GAASmF,SAASlF,IACnC4e,EAAW/S,OAAO7L,SAoBxBugB,aACEhhB,EACAC,EACAC,GAEA,MAAOM,EAASS,GAAWX,EAAgBN,EAAMC,EAAMC,GACjDmf,EAAathB,KAAKshB,WAElB4B,EAAsC,CAC1CvgB,KAAM,YACHF,GAGL,OAAOgJ,EAAcS,OAAM,KACzBoV,EAAWlO,QAAQ3Q,GAASmF,SAASlF,IACnCA,EAAMsM,WAEDhP,KAAKmjB,eAAeD,EAAgBhgB,MAgB/CkgB,cACEnhB,EACAC,EACAC,GAEA,MAAOM,EAASyI,EAAgB,IAAM3I,EAAgBN,EAAMC,EAAMC,QAE9B,IAAzB+I,EAAcnC,SACvBmC,EAAcnC,QAAS,GAGzB,MAAMsa,EAAW5X,EAAcS,OAAM,IACnClM,KAAKshB,WACFlO,QAAQ3Q,GACRod,KAAKnd,GAAUA,EAAMuI,OAAOC,OAGjC,OAAO9E,QAAQkd,IAAID,GAAU5c,KAAK1F,GAAM0J,MAAM1J,GAkBhDwiB,kBACEthB,EACAC,EACAC,GAEA,MAAOM,EAASS,GAAWX,EAAgBN,EAAMC,EAAMC,GAEvD,OAAOsJ,EAAcS,OAAM,KAAM,IAAAsX,EAAAC,EAK/B,GAJAzjB,KAAKshB,WAAWlO,QAAQ3Q,GAASmF,SAASlF,IACxCA,EAAMuN,gBAGoB,SAAxBxN,EAAQihB,YACV,OAAOtd,QAAQC,UAEjB,MAAM6c,EAAsC,IACvCzgB,EACHE,KAA6C,OAAzC6gB,SAAAC,EAAEhhB,EAAQihB,eAAejhB,EAAQE,MAAQ6gB,EAAA,UAE/C,OAAOxjB,KAAKmjB,eAAeD,EAAgBhgB,MAmB/CigB,eACElhB,EACAC,EACAC,GAEA,MAAOM,EAASS,GAAWX,EAAgBN,EAAMC,EAAMC,GAEjDkhB,EAAW5X,EAAcS,OAAM,IACnClM,KAAKshB,WACFlO,QAAQ3Q,GACRnB,QAAQoB,IAAWA,EAAMyM,eACzB0Q,KAAKnd,IAAD,IAAAihB,EAAA,OACHjhB,EAAMwN,WAAM1I,EAAW,IAClBtE,EACHyM,uBAAa,MAAEzM,OAAF,EAAEA,EAASyM,kBACxBrB,KAAM,CAAE+H,YAAa5T,EAAQ4T,oBAKrC,IAAI5M,EAAUrD,QAAQkd,IAAID,GAAU5c,KAAK1F,GAMzC,OAJI,MAACmC,GAAAA,EAASwX,eACZjR,EAAUA,EAAQgB,MAAM1J,IAGnB0I,EA6CT6Y,WAMErgB,EACAC,EAMAC,GAKA,MAAMigB,EAAgBpgB,EAAeC,EAAMC,EAAMC,GAC3CqY,EAAmBxa,KAAK+S,oBAAoBqP,QAGZ,IAA3B5H,EAAiB5P,QAC1B4P,EAAiB5P,OAAQ,GAG3B,MAAMlI,EAAQ1C,KAAKshB,WAAW3O,MAAM3S,KAAMwa,GAE1C,OAAO9X,EAAM4M,cAAckL,EAAiB7Y,WACxCe,EAAMwN,MAAMsK,GACZpU,QAAQC,QAAQ3D,EAAMY,MAAMsD,MA6ClCgd,cAME3hB,EACAC,EAMAC,GAKA,OAAOnC,KAAKsiB,WAAWrgB,EAAaC,EAAaC,GAC9CsE,KAAK1F,GACL0J,MAAM1J,GA6CX8iB,mBAME5hB,EAGAC,EAMAC,GAKA,MAAMigB,EAAgBpgB,EAAeC,EAAMC,EAAMC,GAMjD,OALAigB,EAAc/Q,SAAW2E,IAKlBhW,KAAKsiB,WAAWF,GA6CzB0B,sBAME7hB,EAGAC,EAMAC,GAKA,OAAOnC,KAAK6jB,mBAAmB5hB,EAAaC,EAAaC,GACtDsE,KAAK1F,GACL0J,MAAM1J,GAGX6U,wBACE,OAAO5V,KAAKuT,cAAcqC,wBAG5BmD,gBACE,OAAO/Y,KAAKshB,WAGdhD,mBACE,OAAOte,KAAKuT,cAGdT,YACE,OAAO9S,KAAKwN,OAGduW,oBACE,OAAO/jB,KAAKoN,eAGd4W,kBAAkB9gB,GAChBlD,KAAKoN,eAAiBlK,EAGxB+gB,iBACE5hB,EACAa,GAEA,MAAMqB,EAASvE,KAAKuhB,cAAc/R,MAC/BjO,GAAMoC,EAAatB,KAAcsB,EAAapC,EAAEc,YAE/CkC,EACFA,EAAO6I,eAAiBlK,EAExBlD,KAAKuhB,cAAcxV,KAAK,CAAE1J,WAAU+K,eAAgBlK,IAIxD8P,iBACE3Q,GAEA,IAAKA,EACH,OAIF,MAAM6hB,EAAwBlkB,KAAKuhB,cAAc/R,MAAMjO,GACrD4B,EAAgBd,EAAUd,EAAEc,YAmB9B,OAAA,MAAO6hB,OAAP,EAAOA,EAAuB9W,eAGhC+W,oBACEzgB,EACAR,GAEA,MAAMqB,EAASvE,KAAKwhB,iBAAiBhS,MAClCjO,GAAMoC,EAAaD,KAAiBC,EAAapC,EAAEmC,eAElDa,EACFA,EAAO6I,eAAiBlK,EAExBlD,KAAKwhB,iBAAiBzV,KAAK,CAAErI,cAAa0J,eAAgBlK,IAI9DyS,oBACEjS,GAEA,IAAKA,EACH,OAIF,MAAMwgB,EAAwBlkB,KAAKwhB,iBAAiBhS,MAAMjO,GACxD4B,EAAgBO,EAAanC,EAAEmC,eAmBjC,OAAA,MAAOwgB,OAAP,EAAOA,EAAuB9W,eAGhC2F,oBAOE7P,GAgBA,GAAA,MAAIA,GAAAA,EAASkhB,WACX,OAAOlhB,EAST,MAAMsX,EAAmB,IACpBxa,KAAKoN,eAAeqF,WACpBzS,KAAKgT,uBAAiB9P,SAAAA,EAASb,aAC/Ba,EACHkhB,YAAY,GAmBd,OAhBK5J,EAAiBxX,WAAawX,EAAiBnY,WAClDmY,EAAiBxX,UAAYC,EAC3BuX,EAAiBnY,SACjBmY,SAK+C,IAAxCA,EAAiBhC,qBAC1BgC,EAAiBhC,mBACkB,WAAjCgC,EAAiB3R,kBAE4B,IAAtC2R,EAAiByC,mBAC1BzC,EAAiByC,mBAAqBzC,EAAiBiD,UAGlDjD,EAST9E,uBACExS,GAEA,OAAA,MAAIA,GAAAA,EAASkhB,WACJlhB,EAEF,IACFlD,KAAKoN,eAAeqI,aACpBzV,KAAK2V,0BAAoBzS,SAAAA,EAASQ,gBAClCR,EACHkhB,YAAY,GAIhBlR,QACElT,KAAKshB,WAAWpO,QAChBlT,KAAKuT,cAAcL,2GJj3BhB,SACLN,EACA1P,EAA4B,IAE5B,MAAMuS,EAAkC,GAClChD,EAA6B,GAEnC,IAAmC,IAA/BvP,EAAQmhB,mBAA8B,CACxC,MAAMC,EACJphB,EAAQohB,yBAA2B5G,EAErC9K,EACG0L,mBACAnL,SACAvL,SAASpE,IACJ8gB,EAAwB9gB,IAC1BiS,EAAU1J,KA3CpB,SAA2BvI,GACzB,MAAO,CACLE,YAAaF,EAASN,QAAQQ,YAC9BJ,MAAOE,EAASF,OAwCKihB,CAAkB/gB,OAKzC,IAAiC,IAA7BN,EAAQshB,iBAA4B,CACtC,MAAMC,EACJvhB,EAAQuhB,sBAAwB9G,EAElC/K,EACGmG,gBACA5F,SACAvL,SAASlF,IACJ+hB,EAAqB/hB,IACvB+P,EAAQ1G,KA9ClB,SAAwBrJ,GACtB,MAAO,CACLY,MAAOZ,EAAMY,MACbjB,SAAUK,EAAML,SAChBW,UAAWN,EAAMM,WA0CE0hB,CAAehiB,OAKpC,MAAO,CAAE+S,YAAWhD,wDAGf,SACLG,EACA+R,EACAzhB,GAEA,GAA+B,iBAApByhB,GAAoD,OAApBA,EACzC,OAGF,MAAMpR,EAAgBX,EAAO0L,mBACvBgD,EAAa1O,EAAOmG,gBAGpBtD,EAAakP,EAAoClP,WAAa,GAE9DhD,EAAWkS,EAAoClS,SAAW,GAEhEgD,EAAU7N,SAASgd,IAAuB,IAAAC,EACxCtR,EAAcZ,MACZC,EACA,IACE,MAAG1P,GAAA,OAAH2hB,EAAG3hB,EAASkK,qBAAZ,EAAGyX,EAAyBpP,UAC5B/R,YAAakhB,EAAmBlhB,aAElCkhB,EAAmBthB,UAIvBmP,EAAQ7K,SAAQ,EAAGvF,WAAUiB,QAAON,gBAAgB,IAAA8hB,EAClD,MAAMpiB,EAAQ4e,EAAWtQ,IAAIhO,GAG7B,GAAIN,GACF,GAAIA,EAAMY,MAAMwK,cAAgBxK,EAAMwK,cAAe,CAGnD,MAAQjL,YAAakiB,KAAaC,GAAyB1hB,EAC3DZ,EAAMkM,SAASoW,SAMnB1D,EAAW3O,MACTC,EACA,IACE,MAAG1P,GAAA,OAAH4hB,EAAG5hB,EAASkK,qBAAZ,EAAG0X,EAAyBrS,QAC5BpQ,WACAW,aAIF,IACKM,EACHT,YAAa,4CbuOd,SAAiB5B,GACtB,OAAOA,aAAiBiR,+GArRnB,SAGLjQ,EACAC,EACAC,GAEA,OAAIC,EAAWH,GACO,mBAATC,EACF,IAAKC,EAAMuB,YAAazB,EAAM4R,WAAY3R,GAE5C,IAAKA,EAAMwB,YAAazB,GAGb,mBAATA,EACF,IAAKC,EAAM2R,WAAY5R,GAGzB,IAAKA,8BAgBP,SAILA,EACAC,EACAC,GAEA,OACEC,EAAWH,GACP,CAAC,IAAKC,EAAMwB,YAAazB,GAAQE,GACjC,CAACF,GAAQ,GAAIC"}