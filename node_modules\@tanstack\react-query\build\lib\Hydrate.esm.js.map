{"version": 3, "file": "Hydrate.esm.js", "sources": ["../../src/Hydrate.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport { hydrate } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { HydrateOptions } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\n\nexport function useHydrate(\n  state: unknown,\n  options: HydrateOptions & ContextOptions = {},\n) {\n  const queryClient = useQueryClient({ context: options.context })\n\n  const optionsRef = React.useRef(options)\n  optionsRef.current = options\n\n  // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n  React.useMemo(() => {\n    if (state) {\n      hydrate(queryClient, state, optionsRef.current)\n    }\n  }, [queryClient, state])\n}\n\nexport interface HydrateProps {\n  state?: unknown\n  options?: HydrateOptions\n  children?: React.ReactNode\n}\n\nexport const Hydrate = ({ children, options, state }: HydrateProps) => {\n  useHydrate(state, options)\n  return children as React.ReactElement\n}\n"], "names": ["optionsRef", "state", "useHydrate"], "mappings": ";;;;;AAQO;;;AAI8B;AAEnC;AACAA;AAGA;AACA;AACA;;;AAEE;;AAEC;AACF;AACF;AAQM;;;AAAsCC;AAArB;AACtBC;AACA;AACD;;"}