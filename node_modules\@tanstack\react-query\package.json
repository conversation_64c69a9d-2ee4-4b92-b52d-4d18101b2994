{"name": "@tanstack/react-query", "version": "4.39.2", "description": "Hooks for managing, caching and syncing asynchronous and remote data in React", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/react-query"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "types": "build/lib/index.d.ts", "main": "build/lib/index.js", "module": "build/lib/index.esm.js", "exports": {".": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.mjs", "default": "./build/lib/index.js"}, "./package.json": "./package.json"}, "sideEffects": ["./src/setBatchUpdatesFn.ts"], "files": ["build/lib/*", "build/umd/*", "src", "codemods", "!codemods/jest.config.js", "!codemods/**/__testfixtures__", "!codemods/**/__tests__"], "devDependencies": {"@types/jscodeshift": "^0.11.3", "@types/react": "^18.0.14", "@types/react-dom": "^18.0.5", "@types/use-sync-external-store": "^0.0.3", "react": "^18.2.0", "react-17": "npm:react@^17.0.2", "react-dom": "^18.2.0", "react-dom-17": "npm:react-dom@^17.0.2", "jscodeshift": "^0.13.1", "react-error-boundary": "^3.1.4"}, "dependencies": {"use-sync-external-store": "^1.2.0", "@tanstack/query-core": "4.39.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-native": "*"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}, "scripts": {}}