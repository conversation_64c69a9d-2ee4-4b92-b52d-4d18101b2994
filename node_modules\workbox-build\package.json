{"name": "workbox-build", "version": "7.3.0", "description": "A module that integrates into your build process, helping you generate a manifest of local files that workbox-sw should precache.", "keywords": ["workbox", "workboxjs", "service worker", "caching", "fetch requests", "offline", "file manifest"], "engines": {"node": ">=16.0.0"}, "author": "Google's Web DevRel Team and Google's Aurora Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/googlechrome/workbox.git"}, "bugs": "https://github.com/GoogleChrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "dependencies": {"@apideck/better-ajv-errors": "^0.3.1", "@babel/core": "^7.24.4", "@babel/preset-env": "^7.11.0", "@babel/runtime": "^7.11.2", "@rollup/plugin-babel": "^5.2.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^2.4.1", "@rollup/plugin-terser": "^0.4.3", "@surma/rollup-plugin-off-main-thread": "^2.2.3", "ajv": "^8.6.0", "common-tags": "^1.8.0", "fast-json-stable-stringify": "^2.1.0", "fs-extra": "^9.0.1", "glob": "^7.1.6", "lodash": "^4.17.20", "pretty-bytes": "^5.3.0", "rollup": "^2.43.1", "source-map": "^0.8.0-beta.0", "stringify-object": "^3.3.0", "strip-comments": "^2.0.1", "tempy": "^0.6.0", "upath": "^1.2.0", "workbox-background-sync": "7.3.0", "workbox-broadcast-update": "7.3.0", "workbox-cacheable-response": "7.3.0", "workbox-core": "7.3.0", "workbox-expiration": "7.3.0", "workbox-google-analytics": "7.3.0", "workbox-navigation-preload": "7.3.0", "workbox-precaching": "7.3.0", "workbox-range-requests": "7.3.0", "workbox-recipes": "7.3.0", "workbox-routing": "7.3.0", "workbox-strategies": "7.3.0", "workbox-streams": "7.3.0", "workbox-sw": "7.3.0", "workbox-window": "7.3.0"}, "main": "build/index.js", "workbox": {"packageType": "node_ts"}, "types": "build/index.d.ts", "devDependencies": {"@types/node": "^18.15.11"}, "gitHead": "c77dceb54d4af1749db95316710d6430e82b0c48"}